{"cells": [{"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting imutils\n", "  Using cached https://files.pythonhosted.org/packages/ee/29/6b20a2f2444be479cbd74b8fb0dd2ee92671e1c52360f1cb022c6c00e052/imutils-0.5.1.tar.gz\n", "Building wheels for collected packages: imutils\n", "  Running setup.py bdist_wheel for imutils ... \u001b[?25ldone\n", "\u001b[?25h  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/4c/ff/aa/824fb9efc5b8c740d54cd8bc19c7e85fbb8d115c77e56812c7\n", "Successfully built imutils\n", "Installing collected packages: imutils\n", "Successfully installed imutils-0.5.1\n"]}], "source": ["! pip3 install imutils"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [], "source": ["import glob\n", "import pandas as pd\n", "\n", "import matplotlib.pyplot as plt\n", "from sklearn import datasets, svm, metrics\n", "\n", "from sklearn.model_selection import train_test_split\n", "\n", "import cv2\n", "import imutils\n", "\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [], "source": ["data = []"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "    <tr>\n", "      <th>label</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>17</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       file_name\n", "label           \n", "0             24\n", "1            106\n", "2            123\n", "3             47\n", "4             66\n", "5             70\n", "6             41\n", "7             31\n", "8             18\n", "9             17"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["for i in range(10):\n", "    #for file in glob.glob('Datasets/Digits/'+str(i)+'/*jpg'):\n", "    for file in glob.glob('Datasets/Datasets_digits/'+str(i)+'/*jpg.png'):\n", "        data += [{'file_name':str(file),\n", "                  'label':i}]\n", "        \n", "df = pd.DataFrame(data)\n", "\n", "df.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["train, test = train_test_split(df, stratify=df.label)"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [], "source": ["X_train=[]\n", "for file_name in train.file_name:\n", "    image = cv2.imread(file_name)\n", "    image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)\n", "    #image = cv2.threshold(image, 20, 255, cv2.THRESH_BINARY_INV)[1]\n", "    image.resize(30,50)\n", "    image_flat = image.flatten()\n", "    X_train += [image_flat]\n", "\n", "X_train = np.array(X_train)\n", "\n", "\n", "X_test=[]\n", "for file_name in test.file_name:\n", "    image = cv2.imread(file_name)\n", "    image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)\n", "    image.resize(50,30)\n", "    image = image.flatten()\n", "    X_test += [image]\n", "\n", "X_test = np.array(X_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [{"data": {"text/plain": ["LinearSVC(C=3, class_weight='balanced', dual=False, fit_intercept=True,\n", "     intercept_scaling=1, loss='squared_hinge', max_iter=1000,\n", "     multi_class='ovr', penalty='l1', random_state=None, tol=0.0001,\n", "     verbose=0)"]}, "execution_count": 166, "metadata": {}, "output_type": "execute_result"}], "source": ["classifier = svm.LinearSVC(penalty='l1',C=3, class_weight='balanced',dual=False)\n", "classifier.fit(X_train, train.label)"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Classification report for classifier LinearSVC(C=3, class_weight='balanced', dual=False, fit_intercept=True,\n", "     intercept_scaling=1, loss='squared_hinge', max_iter=1000,\n", "     multi_class='ovr', penalty='l1', random_state=None, tol=0.0001,\n", "     verbose=0):\n", "             precision    recall  f1-score   support\n", "\n", "          0       0.33      0.17      0.22         6\n", "          1       0.48      0.74      0.58        27\n", "          2       0.54      0.42      0.47        31\n", "          3       0.17      0.17      0.17        12\n", "          4       0.62      0.31      0.42        16\n", "          5       0.22      0.28      0.24        18\n", "          6       0.17      0.10      0.12        10\n", "          7       0.00      0.00      0.00         8\n", "          8       0.33      0.25      0.29         4\n", "          9       0.22      0.50      0.31         4\n", "\n", "avg / total       0.38      0.37      0.36       136\n", "\n", "\n", "Confusion matrix:\n", "[[ 1  1  2  0  0  1  0  1  0  0]\n", " [ 0 20  1  1  0  3  0  0  0  2]\n", " [ 0 10 13  4  0  1  2  0  0  1]\n", " [ 0  1  4  2  1  2  1  1  0  0]\n", " [ 0  4  2  1  5  3  0  1  0  0]\n", " [ 1  4  1  3  0  5  1  2  1  0]\n", " [ 0  1  0  0  1  3  1  1  1  2]\n", " [ 0  1  1  1  0  3  0  0  0  2]\n", " [ 1  0  0  0  0  1  1  0  1  0]\n", " [ 0  0  0  0  1  1  0  0  0  2]]\n"]}], "source": ["expected = test.label\n", "predicted = classifier.predict(X_test)\n", "\n", "print(\"Classification report for classifier %s:\\n%s\\n\"\n", "      % (classifier, metrics.classification_report(expected, predicted)))\n", "print(\"Confusion matrix:\\n%s\" % metrics.confusion_matrix(expected, predicted))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 2}