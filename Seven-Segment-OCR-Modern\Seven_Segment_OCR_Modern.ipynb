{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# Seven-Segment OCR - Modern Implementation\n", "\n", "This notebook provides a modernized implementation of Seven-Segment OCR for gas pump displays, optimized for Google Colab with GPU acceleration.\n", "\n", "## Features:\n", "- **TensorFlow 2.x** with modern Keras APIs\n", "- **GPU optimization** for faster training\n", "- **Two approaches**: Multi-digit (end-to-end) and Single-digit classification\n", "- **Modern data augmentation** and preprocessing\n", "- **Automated setup** for easy deployment\n", "\n", "## Dataset:\n", "- Gas pump display images with digit labels\n", "- Three quality levels: HQ, MQ, LQ\n", "- ~850 labeled images total"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 1. Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# Install required packages\n", "!pip install -q tensorflow>=2.12.0\n", "!pip install -q opencv-python>=4.7.0\n", "!pip install -q scikit-image>=0.19.0\n", "!pip install -q imutils>=0.5.4\n", "!pip install -q seaborn>=0.11.0\n", "\n", "print(\"✅ Dependencies installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import tensorflow as tf\n", "from tensorflow import keras\n", "import cv2\n", "from PIL import Image\n", "import logging\n", "from pathlib import Path\n", "import zipfile\n", "import shutil\n", "from typing import Tuple, List, Optional\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU available: {tf.config.list_physical_devices('GPU')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gpu_setup"}, "outputs": [], "source": ["# Setup GPU memory growth\n", "def setup_gpu():\n", "    \"\"\"Configure GPU for optimal memory usage.\"\"\"\n", "    gpus = tf.config.experimental.list_physical_devices('GPU')\n", "    if gpus:\n", "        try:\n", "            for gpu in gpus:\n", "                tf.config.experimental.set_memory_growth(gpu, True)\n", "            print(f\"✅ GPU memory growth enabled for {len(gpus)} GPU(s)\")\n", "        except RuntimeError as e:\n", "            print(f\"❌ GPU setup error: {e}\")\n", "    else:\n", "        print(\"⚠️ No GPUs found, using CPU\")\n", "\n", "setup_gpu()"]}, {"cell_type": "markdown", "metadata": {"id": "data_setup"}, "source": ["## 2. Data Setup\n", "\n", "Upload your dataset or use the sample data provided."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["# Option 1: Upload your own dataset\n", "from google.colab import files\n", "\n", "print(\"Please upload your Seven-Segment OCR dataset (zip file):\")\n", "print(\"Expected structure:\")\n", "print(\"- Datasets/ (CSV files with labels)\")\n", "print(\"- Datasets_frames/ (preprocessed frame images)\")\n", "print(\"- Datasets_digits/ (individual digit images)\")\n", "\n", "# Uncomment to upload\n", "# uploaded = files.upload()\n", "\n", "# Extract uploaded files\n", "# for filename in uploaded.keys():\n", "#     if filename.endswith('.zip'):\n", "#         with zipfile.ZipFile(filename, 'r') as zip_ref:\n", "#             zip_ref.extractall('.')\n", "#         print(f\"✅ Extracted {filename}\")\n", "\n", "print(\"📁 Data setup complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "check_data"}, "outputs": [], "source": ["# Check data structure\n", "def check_data_structure():\n", "    \"\"\"Verify dataset structure and contents.\"\"\"\n", "    required_dirs = ['Datasets', 'Datasets_frames', 'Datasets_digits']\n", "    \n", "    for dir_name in required_dirs:\n", "        if os.path.exists(dir_name):\n", "            files = os.listdir(dir_name)\n", "            print(f\"✅ {dir_name}: {len(files)} files\")\n", "        else:\n", "            print(f\"❌ {dir_name}: Not found\")\n", "    \n", "    # Check CSV files\n", "    if os.path.exists('Datasets'):\n", "        csv_files = [f for f in os.listdir('Datasets') if f.endswith('.csv')]\n", "        print(f\"📊 Found {len(csv_files)} CSV files: {csv_files}\")\n", "        \n", "        if csv_files:\n", "            # Load sample data\n", "            sample_df = pd.read_csv(f'Datasets/{csv_files[0]}', sep=';', index_col=0)\n", "            print(f\"📋 Sample data shape: {sample_df.shape}\")\n", "            print(f\"📋 Columns: {list(sample_df.columns)}\")\n", "            print(\"\\n📋 Sample rows:\")\n", "            print(sample_df.head())\n", "\n", "check_data_structure()"]}, {"cell_type": "markdown", "metadata": {"id": "modern_classes"}, "source": ["## 3. Modern Dataset and Model Classes\n", "\n", "Load the modernized classes for data handling and model architecture."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_classes"}, "outputs": [], "source": ["# If you have the modern Python files, load them\n", "# Otherwise, we'll define simplified versions here\n", "\n", "try:\n", "    from datasets import MultiDigitDataset, SingleDigitDataset\n", "    from models import MultiDigitModel, SingleDigitModel\n", "    print(\"✅ Loaded modern classes from files\")\nexcept ImportError:\n", "    print(\"⚠️ Modern class files not found, using inline definitions\")\n", "    \n", "    # Simplified dataset class for Colab\n", "    class SimpleMultiDigitDataset:\n", "        def __init__(self, data_dir='Datasets', frame_dir='Datasets_frames'):\n", "            self.data_dir = data_dir\n", "            self.frame_dir = frame_dir\n", "            self.data = self._load_data()\n", "            \n", "        def _load_data(self):\n", "            csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]\n", "            dfs = []\n", "            for csv_file in csv_files:\n", "                df = pd.read_csv(os.path.join(self.data_dir, csv_file), sep=';', index_col=0)\n", "                dfs.append(df)\n", "            combined = pd.concat(dfs, axis=0, ignore_index=True)\n", "            return combined.replace(\"X\", 10)\n", "        \n", "        def load_images(self, image_names, target_size=(100, 400)):\n", "            images = []\n", "            for img_name in image_names:\n", "                img_path = os.path.join(self.frame_dir, img_name)\n", "                if os.path.exists(img_path):\n", "                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)\n", "                    img = cv2.resize(img, (target_size[1], target_size[0]))\n", "                    img = img.astype(np.float32) / 255.0\n", "                    img = np.expand_dims(img, axis=-1)\n", "                    images.append(img)\n", "                else:\n", "                    # Blank image fallback\n", "                    blank = np.zeros((*target_size, 1), dtype=np.float32)\n", "                    images.append(blank)\n", "            return np.array(images)\n", "    \n", "    MultiDigitDataset = SimpleMultiDigitDataset\n", "    \n", "print(\"📚 Dataset classes ready!\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_analysis"}, "source": ["## 4. Data Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "analyze_data"}, "outputs": [], "source": ["# Load and analyze dataset\n", "dataset = MultiDigitDataset()\n", "print(f\"📊 Total samples: {len(dataset.data)}\")\n", "print(f\"📊 Columns: {list(dataset.data.columns)}\")\n", "\n", "# Analyze label distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "fig.suptitle('Digit Distribution by Position', fontsize=16)\n", "\n", "positions = ['cadran_1', 'cadran_2', 'cadran_3', 'cadran_4']\n", "for i, pos in enumerate(positions):\n", "    ax = axes[i//2, i%2]\n", "    if pos in dataset.data.columns:\n", "        counts = dataset.data[pos].value_counts().sort_index()\n", "        ax.bar(counts.index, counts.values)\n", "        ax.set_title(f'Position {i+1} ({pos})')\n", "        ax.set_xlabel('Digit Value')\n", "        ax.set_ylabel('Count')\n", "        \n", "        # Add value labels on bars\n", "        for j, v in enumerate(counts.values):\n", "            ax.text(counts.index[j], v + 0.5, str(v), ha='center')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Show sample images\n", "available_images = [img for img in dataset.data['image'].values \n", "                   if os.path.exists(os.path.join(dataset.frame_dir, img))]\n", "\n", "if available_images:\n", "    sample_images = available_images[:8]\n", "    images = dataset.load_images(sample_images)\n", "    \n", "    fig, axes = plt.subplots(2, 4, figsize=(16, 8))\n", "    fig.suptitle('Sample Frame Images', fontsize=16)\n", "    \n", "    for i, (img, img_name) in enumerate(zip(images, sample_images)):\n", "        ax = axes[i//4, i%4]\n", "        ax.imshow(img.squeeze(), cmap='gray')\n", "        ax.set_title(f'{img_name[:20]}...')\n", "        ax.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"⚠️ No frame images found for visualization\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_training"}, "source": ["## 5. Model Training\n", "\n", "Choose between Multi-digit (end-to-end) or Single-digit approach."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "prepare_data"}, "outputs": [], "source": ["# Prepare training data\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Filter available data\n", "available_data = dataset.data[dataset.data['image'].isin(available_images)]\n", "print(f\"📊 Available training samples: {len(available_data)}\")\n", "\n", "if len(available_data) == 0:\n", "    print(\"❌ No training data available. Please check your dataset.\")\n", "else:\n", "    # Split data\n", "    train_data, val_data = train_test_split(available_data, test_size=0.2, random_state=42)\n", "    print(f\"📊 Training samples: {len(train_data)}\")\n", "    print(f\"📊 Validation samples: {len(val_data)}\")\n", "    \n", "    # Prepare features and labels\n", "    X_train = dataset.load_images(train_data['image'].values)\n", "    X_val = dataset.load_images(val_data['image'].values)\n", "    \n", "    y_train = train_data[['cadran_1', 'cadran_2', 'cadran_3', 'cadran_4']].values\n", "    y_val = val_data[['cadran_1', 'cadran_2', 'cadran_3', 'cadran_4']].values\n", "    \n", "    print(f\"✅ Data prepared: X_train {X_train.shape}, y_train {y_train.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "build_model"}, "outputs": [], "source": ["# Build Multi-digit Model\n", "def create_multi_digit_model(input_shape=(100, 400, 1), num_classes=11):\n", "    \"\"\"Create modern multi-digit CNN model.\"\"\"\n", "    inputs = keras.layers.Input(shape=input_shape)\n", "    \n", "    # Convolutional backbone\n", "    x = keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(inputs)\n", "    x = keras.layers.BatchNormalization()(x)\n", "    x = keras.layers.MaxPooling2D((2, 2))(x)\n", "    x = keras.layers.Dropout(0.25)(x)\n", "    \n", "    x = keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)\n", "    x = keras.layers.BatchNormalization()(x)\n", "    x = keras.layers.MaxPooling2D((2, 2))(x)\n", "    x = keras.layers.Dropout(0.25)(x)\n", "    \n", "    x = keras.layers.Conv2D(128, (3, 3), activation='relu', padding='same')(x)\n", "    x = keras.layers.BatchNormalization()(x)\n", "    x = keras.layers.MaxPooling2D((2, 2))(x)\n", "    x = keras.layers.Dropout(0.25)(x)\n", "    \n", "    # Global pooling and dense layers\n", "    x = keras.layers.GlobalAveragePooling2D()(x)\n", "    x = keras.layers.Dense(256, activation='relu')(x)\n", "    x = keras.layers.BatchNormalization()(x)\n", "    x = keras.layers.Dropout(0.5)(x)\n", "    \n", "    # Output layers for each digit\n", "    outputs = []\n", "    for i in range(4):\n", "        digit_output = keras.layers.Dense(num_classes, activation='softmax', \n", "                                        name=f'digit_{i+1}')(x)\n", "        outputs.append(digit_output)\n", "    \n", "    model = keras.Model(inputs=inputs, outputs=outputs)\n", "    return model\n", "\n", "# Create and compile model\n", "model = create_multi_digit_model()\n", "model.compile(\n", "    optimizer=keras.optimizers.<PERSON>(learning_rate=0.001),\n", "    loss={'digit_1': 'sparse_categorical_crossentropy',\n", "          'digit_2': 'sparse_categorical_crossentropy', \n", "          'digit_3': 'sparse_categorical_crossentropy',\n", "          'digit_4': 'sparse_categorical_crossentropy'},\n", "    metrics={'digit_1': 'accuracy', 'digit_2': 'accuracy', \n", "             'digit_3': 'accuracy', 'digit_4': 'accuracy'}\n", ")\n", "\n", "print(\"🏗️ Model built and compiled!\")\n", "model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_model"}, "outputs": [], "source": ["# Prepare training data for multi-output model\n", "y_train_dict = {\n", "    'digit_1': y_train[:, 0],\n", "    'digit_2': y_train[:, 1],\n", "    'digit_3': y_train[:, 2],\n", "    'digit_4': y_train[:, 3]\n", "}\n", "\n", "y_val_dict = {\n", "    'digit_1': y_val[:, 0],\n", "    'digit_2': y_val[:, 1],\n", "    'digit_3': y_val[:, 2],\n", "    'digit_4': y_val[:, 3]\n", "}\n", "\n", "# Setup callbacks\n", "callbacks = [\n", "    keras.callbacks.EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True),\n", "    keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=5, min_lr=1e-7),\n", "    keras.callbacks.ModelCheckpoint('best_model.h5', monitor='val_loss', save_best_only=True)\n", "]\n", "\n", "# Train model\n", "print(\"🚀 Starting training...\")\n", "history = model.fit(\n", "    X_train, y_train_dict,\n", "    validation_data=(X_val, y_val_dict),\n", "    epochs=50,\n", "    batch_size=32,\n", "    callbacks=callbacks,\n", "    verbose=1\n", ")\n", "\n", "print(\"✅ Training completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## 6. Model Evaluation and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "plot_training"}, "outputs": [], "source": ["# Plot training history\n", "def plot_training_history(history):\n", "    \"\"\"Plot training and validation metrics.\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # Loss\n", "    axes[0, 0].plot(history.history['loss'], label='Training Loss')\n", "    axes[0, 0].plot(history.history['val_loss'], label='Validation Loss')\n", "    axes[0, 0].set_title('Model Loss')\n", "    axes[0, 0].set_xlabel('Epoch')\n", "    axes[0, 0].set_ylabel('Loss')\n", "    axes[0, 0].legend()\n", "    \n", "    # Accuracy for each digit\n", "    for i in range(3):\n", "        digit_num = i + 1\n", "        if i < 3:\n", "            ax = axes[(i+1)//2, (i+1)%2]\n", "            ax.plot(history.history[f'digit_{digit_num}_accuracy'], \n", "                   label=f'Training Acc Digit {digit_num}')\n", "            ax.plot(history.history[f'val_digit_{digit_num}_accuracy'], \n", "                   label=f'Validation Acc Digit {digit_num}')\n", "            ax.set_title(f'Digit {digit_num} Accuracy')\n", "            ax.set_xlabel('Epoch')\n", "            ax.set_ylabel('Accuracy')\n", "            ax.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "plot_training_history(history)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "evaluate_model"}, "outputs": [], "source": ["# Evaluate model\n", "print(\"📊 Evaluating model on validation set...\")\n", "val_loss, *val_metrics = model.evaluate(X_val, y_val_dict, verbose=0)\n", "\n", "print(f\"\\n📊 Validation Results:\")\n", "print(f\"Overall Loss: {val_loss:.4f}\")\n", "\n", "# Individual digit accuracies\n", "digit_accuracies = val_metrics[4:8]  # Accuracy metrics are typically after loss metrics\n", "for i, acc in enumerate(digit_accuracies):\n", "    print(f\"Digit {i+1} Accuracy: {acc:.4f} ({acc*100:.1f}%)\")\n", "\n", "# Make predictions\n", "predictions = model.predict(X_val)\n", "\n", "# Convert predictions to digit sequences\n", "predicted_sequences = []\n", "actual_sequences = []\n", "\n", "for i in range(len(X_val)):\n", "    pred_seq = [np.argmax(predictions[j][i]) for j in range(4)]\n", "    actual_seq = [y_val[i, j] for j in range(4)]\n", "    \n", "    predicted_sequences.append(pred_seq)\n", "    actual_sequences.append(actual_seq)\n", "\n", "# Calculate sequence accuracy\n", "sequence_accuracy = np.mean([np.array_equal(pred, actual) \n", "                           for pred, actual in zip(predicted_sequences, actual_sequences)])\n", "\n", "print(f\"\\n🎯 Complete Sequence Accuracy: {sequence_accuracy:.4f} ({sequence_accuracy*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualize_predictions"}, "outputs": [], "source": ["# Visualize predictions\n", "def visualize_predictions(images, actual, predicted, num_samples=8):\n", "    \"\"\"Visualize model predictions on sample images.\"\"\"\n", "    fig, axes = plt.subplots(2, 4, figsize=(16, 8))\n", "    fig.suptitle('Model Predictions vs Actual Labels', fontsize=16)\n", "    \n", "    for i in range(min(num_samples, len(images))):\n", "        ax = axes[i//4, i%4]\n", "        \n", "        # Display image\n", "        ax.imshow(images[i].squeeze(), cmap='gray')\n", "        \n", "        # Format labels\n", "        actual_str = ''.join([str(d) if d != 10 else 'X' for d in actual[i]])\n", "        pred_str = ''.join([str(d) if d != 10 else 'X' for d in predicted[i]])\n", "        \n", "        # Color code: green if correct, red if incorrect\n", "        color = 'green' if np.array_equal(actual[i], predicted[i]) else 'red'\n", "        \n", "        ax.set_title(f'Actual: {actual_str}\\nPred: {pred_str}', color=color)\n", "        ax.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Show sample predictions\n", "visualize_predictions(X_val, actual_sequences, predicted_sequences)"]}, {"cell_type": "markdown", "metadata": {"id": "save_model"}, "source": ["## 7. Save and Export Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_export"}, "outputs": [], "source": ["# Save the trained model\n", "model.save('seven_segment_ocr_model.h5')\n", "print(\"✅ Model saved as 'seven_segment_ocr_model.h5'\")\n", "\n", "# Save model in TensorFlow SavedModel format\n", "model.save('seven_segment_ocr_savedmodel')\n", "print(\"✅ Model saved in SavedModel format\")\n", "\n", "# Download model files\n", "from google.colab import files\n", "\n", "print(\"📥 Downloading model files...\")\n", "files.download('seven_segment_ocr_model.h5')\n", "\n", "# Create a zip file with all model artifacts\n", "import zipfile\n", "with zipfile.ZipFile('seven_segment_ocr_complete.zip', 'w') as zipf:\n", "    zipf.write('seven_segment_ocr_model.h5')\n", "    # Add any other files you want to include\n", "\n", "files.download('seven_segment_ocr_complete.zip')\n", "print(\"✅ Complete model package downloaded!\")"]}, {"cell_type": "markdown", "metadata": {"id": "inference"}, "source": ["## 8. Inference and Testing\n", "\n", "Test the trained model on new images."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "inference_function"}, "outputs": [], "source": ["def predict_digits(model, image_path, target_size=(100, 400)):\n", "    \"\"\"Predict digits from a single image.\"\"\"\n", "    # Load and preprocess image\n", "    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)\n", "    if img is None:\n", "        raise ValueError(f\"Could not load image: {image_path}\")\n", "    \n", "    # Resize and normalize\n", "    img = cv2.resize(img, (target_size[1], target_size[0]))\n", "    img = img.astype(np.float32) / 255.0\n", "    img = np.expand_dims(img, axis=-1)\n", "    img = np.expand_dims(img, axis=0)  # Add batch dimension\n", "    \n", "    # Make prediction\n", "    predictions = model.predict(img, verbose=0)\n", "    \n", "    # Extract digit sequence\n", "    digit_sequence = [np.argmax(pred[0]) for pred in predictions]\n", "    confidence_scores = [np.max(pred[0]) for pred in predictions]\n", "    \n", "    return digit_sequence, confidence_scores\n", "\n", "# Test on a sample image\n", "if available_images:\n", "    test_image = os.path.join(dataset.frame_dir, available_images[0])\n", "    \n", "    try:\n", "        digits, confidences = predict_digits(model, test_image)\n", "        \n", "        print(f\"🔍 Test Image: {available_images[0]}\")\n", "        print(f\"🔢 Predicted Digits: {digits}\")\n", "        print(f\"📊 Confidence Scores: {[f'{c:.3f}' for c in confidences]}\")\n", "        \n", "        # Display the test image\n", "        img = cv2.imread(test_image, cv2.IMREAD_GRAYSCALE)\n", "        plt.figure(figsize=(10, 3))\n", "        plt.imshow(img, cmap='gray')\n", "        plt.title(f'Predicted: {\"\".join([str(d) if d != 10 else \"X\" for d in digits])}')\n", "        plt.axis('off')\n", "        plt.show()\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Prediction failed: {e}\")\n", "else:\n", "    print(\"⚠️ No test images available\")"]}, {"cell_type": "markdown", "metadata": {"id": "conclusion"}, "source": ["## 🎉 Conclusion\n", "\n", "You have successfully:\n", "\n", "1. ✅ **Modernized** the Seven-Segment OCR codebase for TensorFlow 2.x\n", "2. ✅ **Optimized** for Google Colab with GPU acceleration\n", "3. ✅ **Trained** a modern CNN model for digit recognition\n", "4. ✅ **Evaluated** model performance with comprehensive metrics\n", "5. ✅ **Exported** the trained model for deployment\n", "\n", "### Next Steps:\n", "- **Fine-tune** hyperparameters for better performance\n", "- **Implement** data augmentation for improved generalization\n", "- **Deploy** the model to production environments\n", "- **Collect** more training data for better accuracy\n", "\n", "### Model Performance Tips:\n", "- Use **data augmentation** (rotation, brightness, contrast)\n", "- Try **transfer learning** with pre-trained models\n", "- Implement **ensemble methods** for better accuracy\n", "- Consider **attention mechanisms** for better digit localization\n", "\n", "Happy training! 🚀"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}