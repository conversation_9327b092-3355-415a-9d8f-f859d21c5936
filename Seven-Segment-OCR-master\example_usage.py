#!/usr/bin/env python3
"""
Example usage of the Simple OCR interface for Seven-Segment displays
"""

from simple_ocr import SimpleOC<PERSON>, ocr_digit, load_colab_model
import os

def main():
    print("🔍 Seven-Segment OCR - Example Usage")
    print("=" * 50)
    
    # Method 1: Quick single function call
    print("\n📋 Method 1: Quick OCR function")
    print("-" * 30)
    
    # Example with a test image (if available)
    test_images = []
    if os.path.exists("test"):
        test_images = [f"test/{f}" for f in os.listdir("test") if f.endswith('.jpg')]
    
    if test_images:
        test_image = test_images[0]
        print(f"🧪 Testing on: {test_image}")
        
        # Quick OCR call (will prompt to train if no model exists)
        result = ocr_digit(test_image, confidence_threshold=0.6)
        print(f"📊 Result: {result}")
    
    # Method 2: Using your Google Colab trained model
    print("\n📋 Method 2: Using Google Colab Model")
    print("-" * 40)
    
    # Check if you have the Colab model file
    colab_model_files = [
        "single_digit_ocr_model.keras",
        "single_digit_ocr_model.h5"
    ]
    
    colab_model_path = None
    for model_file in colab_model_files:
        if os.path.exists(model_file):
            colab_model_path = model_file
            break
    
    if colab_model_path:
        print(f"✅ Found Colab model: {colab_model_path}")
        
        # Load your trained model
        ocr = load_colab_model(colab_model_path)
        
        # Test on images
        if test_images:
            for i, test_image in enumerate(test_images[:3]):  # Test first 3 images
                print(f"\n🧪 Test {i+1}: {test_image}")
                result = ocr.simple_ocr(test_image, confidence_threshold=0.7)
                print(f"   📊 Digit: {result['digit']}")
                print(f"   📊 Confidence: {result['confidence']:.3f}")
                print(f"   📊 Status: {result['status']}")
    else:
        print("❌ No Colab model found. Expected files:")
        for model_file in colab_model_files:
            print(f"   - {model_file}")
        print("\n💡 Download your trained model from Google Colab first!")
    
    # Method 3: Class-based usage with more control
    print("\n📋 Method 3: Class-based Usage")
    print("-" * 35)
    
    # Initialize OCR system
    ocr = SimpleOCR(model_type='single')
    
    # Load model or train new one
    if colab_model_path:
        ocr.load_model(colab_model_path)
    else:
        print("🚀 Training new model with existing dataset...")
        success = ocr.train_new_model(epochs=5)  # Quick training
        if success:
            # Save the trained model
            ocr.save_model("newly_trained_model.keras")
    
    # Test different confidence thresholds
    if test_images and ocr.model:
        test_image = test_images[0]
        print(f"\n🧪 Testing different thresholds on: {test_image}")
        
        thresholds = [0.5, 0.7, 0.9]
        for threshold in thresholds:
            result = ocr.simple_ocr(test_image, confidence_threshold=threshold)
            print(f"   Threshold {threshold}: {result['digit']} ({result['confidence']:.3f}) - {result['status']}")
    
    # Method 4: Batch processing
    print("\n📋 Method 4: Batch Processing")
    print("-" * 32)
    
    if test_images and ocr.model:
        print("🔄 Processing all test images...")
        
        results = []
        for image_path in test_images:
            result = ocr.simple_ocr(image_path, confidence_threshold=0.7)
            results.append({
                'image': os.path.basename(image_path),
                'digit': result['digit'],
                'confidence': result['confidence'],
                'status': result['status']
            })
        
        # Display results
        print("\n📊 Batch Results:")
        print(f"{'Image':<40} {'Digit':<6} {'Confidence':<12} {'Status'}")
        print("-" * 70)
        for r in results:
            print(f"{r['image']:<40} {r['digit']:<6} {r['confidence']:<12.3f} {r['status']}")
        
        # Summary
        confident_count = sum(1 for r in results if r['status'] == 'confident')
        print(f"\n📈 Summary: {confident_count}/{len(results)} predictions were confident")
    
    print("\n" + "=" * 50)
    print("✅ Example usage completed!")
    print("\n💡 Quick Usage Tips:")
    print("   • For single image: result = ocr_digit('image.jpg', 0.7)")
    print("   • For Colab model: ocr = load_colab_model('model.keras')")
    print("   • For batch: use ocr.simple_ocr() in a loop")
    print("   • Adjust threshold based on your accuracy needs")

if __name__ == "__main__":
    main()
