{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/lib/python3.6/site-packages/h5py/__init__.py:36: FutureWarning: Conversion of the second argument of issubdtype from `float` to `np.floating` is deprecated. In future, it will be treated as `np.float64 == np.dtype(float).type`.\n", "  from ._conv import register_converters as _register_converters\n", "Using TensorFlow backend.\n", "/home/<USER>/anaconda3/lib/python3.6/site-packages/sklearn/cross_validation.py:41: DeprecationWarning: This module was deprecated in version 0.18 in favor of the model_selection module into which all the refactored classes and functions are moved. Also note that the interface of the new CV iterators are different from that of this module. This module will be removed in 0.20.\n", "  \"This module will be removed in 0.20.\", DeprecationWarning)\n", "/home/<USER>/Seven-Segment-OCR/Model.py:18: UserWarning: \n", "This call to matplotlib.use() has no effect because the backend has already\n", "been chosen; matplotlib.use() must be called *before* pylab, matplotlib.pyplot,\n", "or matplotlib.backends is imported for the first time.\n", "\n", "The backend was *originally* set to 'module://ipykernel.pylab.backend_inline' by the following code:\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/runpy.py\", line 193, in _run_module_as_main\n", "    \"__main__\", mod_spec)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/runpy.py\", line 85, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/ipykernel_launcher.py\", line 16, in <module>\n", "    app.launch_new_instance()\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/traitlets/config/application.py\", line 658, in launch_instance\n", "    app.start()\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/ipykernel/kernelapp.py\", line 478, in start\n", "    self.io_loop.start()\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/zmq/eventloop/ioloop.py\", line 177, in start\n", "    super(Z<PERSON><PERSON><PERSON><PERSON><PERSON>, self).start()\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/tornado/ioloop.py\", line 888, in start\n", "    handler_func(fd_obj, events)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/tornado/stack_context.py\", line 277, in null_wrapper\n", "    return fn(*args, **kwargs)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/zmq/eventloop/zmqstream.py\", line 440, in _handle_events\n", "    self._handle_recv()\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/zmq/eventloop/zmqstream.py\", line 472, in _handle_recv\n", "    self._run_callback(callback, msg)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/zmq/eventloop/zmqstream.py\", line 414, in _run_callback\n", "    callback(*args, **kwargs)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/tornado/stack_context.py\", line 277, in null_wrapper\n", "    return fn(*args, **kwargs)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/ipykernel/kernelbase.py\", line 283, in dispatcher\n", "    return self.dispatch_shell(stream, msg)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/ipykernel/kernelbase.py\", line 233, in dispatch_shell\n", "    handler(stream, idents, msg)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/ipykernel/kernelbase.py\", line 399, in execute_request\n", "    user_expressions, allow_stdin)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/ipykernel/ipkernel.py\", line 208, in do_execute\n", "    res = shell.run_cell(code, store_history=store_history, silent=silent)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/ipykernel/zmqshell.py\", line 537, in run_cell\n", "    return super(ZMQInteractiveShell, self).run_cell(*args, **kwargs)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/IPython/core/interactiveshell.py\", line 2728, in run_cell\n", "    interactivity=interactivity, compiler=compiler, result=result)\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/IPython/core/interactiveshell.py\", line 2850, in run_ast_nodes\n", "    if self.run_code(code, result):\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/IPython/core/interactiveshell.py\", line 2910, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"<ipython-input-1-886a08fc224d>\", line 2, in <module>\n", "    from Model import Model_Multi, Model_Single\n", "  File \"/home/<USER>/Seven-Segment-OCR/Model.py\", line 16, in <module>\n", "    from Datasets import Dataset_Multi, Dataset_Single\n", "  File \"/home/<USER>/Seven-Segment-OCR/Datasets.py\", line 4, in <module>\n", "    import matplotlib.pyplot as plt\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/matplotlib/pyplot.py\", line 71, in <module>\n", "    from matplotlib.backends import pylab_setup\n", "  File \"/home/<USER>/anaconda3/lib/python3.6/site-packages/matplotlib/backends/__init__.py\", line 16, in <module>\n", "    line for line in traceback.format_stack()\n", "\n", "\n", "  matplotlib.use('agg')\n", "/home/<USER>/Seven-Segment-OCR/Model.py:81: UserWarning: Update your `Dense` call to the Keras 2 API: `Dense(activation=\"softmax\", name=\"digit_1\", units=11)`\n", "  digit1 = (Dense(output_dim =11,activation = 'softmax', name='digit_1'))(x)\n", "/home/<USER>/Seven-Segment-OCR/Model.py:82: UserWarning: Update your `Dense` call to the Keras 2 API: `Dense(activation=\"softmax\", name=\"digit_2\", units=11)`\n", "  digit2 = (Dense(output_dim =11,activation = 'softmax', name='digit_2'))(x)\n", "/home/<USER>/Seven-Segment-OCR/Model.py:83: UserWarning: Update your `Dense` call to the Keras 2 API: `Dense(activation=\"softmax\", name=\"digit_3\", units=11)`\n", "  digit3 = (Dense(output_dim =11,activation = 'softmax', name='digit_3'))(x)\n", "/home/<USER>/Seven-Segment-OCR/Model.py:84: UserWarning: Update your `Dense` call to the Keras 2 API: `Dense(activation=\"softmax\", name=\"digit_4\", units=11)`\n", "  digit4 = (Dense(output_dim =11,activation = 'softmax', name='digit_4'))(x)\n", "/home/<USER>/Seven-Segment-OCR/Model.py:88: UserWarning: Update your `Model` call to the Keras 2 API: `Model(inputs=Tensor(\"in..., outputs=[<tf.Tenso...)`\n", "  self.model = keras.models.Model(input = model_input , output = outputs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From /home/<USER>/anaconda3/lib/python3.6/site-packages/tensorflow/python/util/tf_should_use.py:118: initialize_all_variables (from tensorflow.python.ops.variables) is deprecated and will be removed after 2017-03-02.\n", "Instructions for updating:\n", "Use `tf.global_variables_initializer` instead.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Seven-Segment-OCR/Model.py:95: UserWarning: The `nb_epoch` argument in `fit` has been renamed `epochs`.\n", "  self.history = self.model.fit(self.X_train, self.y_train_vect, batch_size= 50, nb_epoch=epochs, verbose=1, validation_data=(self.X_val, self.y_val_vect))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Variable *= will be deprecated. Use variable.assign_mul if you want assignment to the variable value or 'x = x * y' if you want a new python Tensor object.\n", "Train on 516 samples, validate on 172 samples\n", "Epoch 1/50\n", "516/516 [==============================] - 10s 19ms/step - loss: 16.5210 - digit_1_loss: 0.3127 - digit_2_loss: 2.8536 - digit_3_loss: 3.6920 - digit_4_loss: 3.5457 - digit_1_acc: 0.9070 - digit_2_acc: 0.4593 - digit_3_acc: 0.1570 - digit_4_acc: 0.1047 - val_loss: 12.2617 - val_digit_1_loss: 1.0729e-06 - val_digit_2_loss: 0.9464 - val_digit_3_loss: 2.3587 - val_digit_4_loss: 2.9058 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.6395 - val_digit_3_acc: 0.1512 - val_digit_4_acc: 0.1163\n", "Epoch 2/50\n", "516/516 [==============================] - 0s 902us/step - loss: 12.4557 - digit_1_loss: 1.0729e-06 - digit_2_loss: 1.0329 - digit_3_loss: 2.6354 - digit_4_loss: 2.8207 - digit_1_acc: 1.0000 - digit_2_acc: 0.7171 - digit_3_acc: 0.1880 - digit_4_acc: 0.1512 - val_loss: 11.6260 - val_digit_1_loss: 1.0729e-06 - val_digit_2_loss: 0.8572 - val_digit_3_loss: 2.4984 - val_digit_4_loss: 2.4349 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7616 - val_digit_3_acc: 0.1802 - val_digit_4_acc: 0.1395\n", "Epoch 3/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 11.1871 - digit_1_loss: 1.0729e-06 - digit_2_loss: 0.8058 - digit_3_loss: 2.3470 - digit_4_loss: 2.3099 - digit_1_acc: 1.0000 - digit_2_acc: 0.7636 - digit_3_acc: 0.2616 - digit_4_acc: 0.2248 - val_loss: 10.6026 - val_digit_1_loss: 1.0736e-06 - val_digit_2_loss: 0.8218 - val_digit_3_loss: 1.9626 - val_digit_4_loss: 2.2490 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7791 - val_digit_3_acc: 0.3430 - val_digit_4_acc: 0.2267\n", "Epoch 4/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 10.3509 - digit_1_loss: 1.0729e-06 - digit_2_loss: 0.7577 - digit_3_loss: 1.9547 - digit_4_loss: 2.1854 - digit_1_acc: 1.0000 - digit_2_acc: 0.7422 - digit_3_acc: 0.3508 - digit_4_acc: 0.2636 - val_loss: 10.0480 - val_digit_1_loss: 1.1353e-06 - val_digit_2_loss: 0.7223 - val_digit_3_loss: 1.7827 - val_digit_4_loss: 2.2468 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7965 - val_digit_3_acc: 0.4244 - val_digit_4_acc: 0.1919\n", "Epoch 5/50\n", "516/516 [==============================] - 0s 955us/step - loss: 9.5146 - digit_1_loss: 1.0729e-06 - digit_2_loss: 0.6234 - digit_3_loss: 1.6826 - digit_4_loss: 2.0249 - digit_1_acc: 1.0000 - digit_2_acc: 0.8081 - digit_3_acc: 0.4167 - digit_4_acc: 0.2868 - val_loss: 9.6984 - val_digit_1_loss: 2.0958e-06 - val_digit_2_loss: 0.7341 - val_digit_3_loss: 1.8033 - val_digit_4_loss: 2.1271 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7965 - val_digit_3_acc: 0.4186 - val_digit_4_acc: 0.2558\n", "Epoch 6/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 8.9493 - digit_1_loss: 1.0729e-06 - digit_2_loss: 0.5566 - digit_3_loss: 1.6243 - digit_4_loss: 1.8404 - digit_1_acc: 1.0000 - digit_2_acc: 0.8198 - digit_3_acc: 0.4651 - digit_4_acc: 0.3314 - val_loss: 9.4409 - val_digit_1_loss: 5.6431e-06 - val_digit_2_loss: 0.7123 - val_digit_3_loss: 1.7257 - val_digit_4_loss: 2.2146 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8140 - val_digit_3_acc: 0.3837 - val_digit_4_acc: 0.2326\n", "Epoch 7/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 8.5011 - digit_1_loss: 1.0729e-06 - digit_2_loss: 0.5499 - digit_3_loss: 1.4567 - digit_4_loss: 1.8048 - digit_1_acc: 1.0000 - digit_2_acc: 0.8101 - digit_3_acc: 0.5019 - digit_4_acc: 0.4012 - val_loss: 9.0521 - val_digit_1_loss: 9.7810e-06 - val_digit_2_loss: 0.6641 - val_digit_3_loss: 1.6052 - val_digit_4_loss: 2.2237 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8198 - val_digit_3_acc: 0.4302 - val_digit_4_acc: 0.2093\n", "Epoch 8/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 7.9541 - digit_1_loss: 1.0752e-06 - digit_2_loss: 0.4906 - digit_3_loss: 1.3211 - digit_4_loss: 1.6748 - digit_1_acc: 1.0000 - digit_2_acc: 0.8450 - digit_3_acc: 0.5620 - digit_4_acc: 0.4380 - val_loss: 8.8649 - val_digit_1_loss: 1.0757e-05 - val_digit_2_loss: 0.7967 - val_digit_3_loss: 1.6308 - val_digit_4_loss: 2.0907 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7674 - val_digit_3_acc: 0.4360 - val_digit_4_acc: 0.3081\n", "Epoch 9/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 7.5495 - digit_1_loss: 1.0740e-06 - digit_2_loss: 0.4623 - digit_3_loss: 1.2656 - digit_4_loss: 1.5596 - digit_1_acc: 1.0000 - digit_2_acc: 0.8391 - digit_3_acc: 0.5930 - digit_4_acc: 0.4806 - val_loss: 8.5327 - val_digit_1_loss: 1.2069e-05 - val_digit_2_loss: 0.6604 - val_digit_3_loss: 1.6362 - val_digit_4_loss: 2.0862 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8081 - val_digit_3_acc: 0.4419 - val_digit_4_acc: 0.3081\n", "Epoch 10/50\n", "516/516 [==============================] - 0s 919us/step - loss: 7.0758 - digit_1_loss: 1.0750e-06 - digit_2_loss: 0.4297 - digit_3_loss: 1.1462 - digit_4_loss: 1.4291 - digit_1_acc: 1.0000 - digit_2_acc: 0.8566 - digit_3_acc: 0.6240 - digit_4_acc: 0.5136 - val_loss: 8.2938 - val_digit_1_loss: 1.6559e-05 - val_digit_2_loss: 0.7455 - val_digit_3_loss: 1.4775 - val_digit_4_loss: 2.1040 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7907 - val_digit_3_acc: 0.5465 - val_digit_4_acc: 0.2791\n", "Epoch 11/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 6.8161 - digit_1_loss: 1.0770e-06 - digit_2_loss: 0.3949 - digit_3_loss: 1.0927 - digit_4_loss: 1.4345 - digit_1_acc: 1.0000 - digit_2_acc: 0.8643 - digit_3_acc: 0.6415 - digit_4_acc: 0.5465 - val_loss: 8.1714 - val_digit_1_loss: 1.7901e-05 - val_digit_2_loss: 0.6946 - val_digit_3_loss: 1.5644 - val_digit_4_loss: 2.1144 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7907 - val_digit_3_acc: 0.4419 - val_digit_4_acc: 0.2849\n", "Epoch 12/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 6.3089 - digit_1_loss: 1.1057e-06 - digit_2_loss: 0.3460 - digit_3_loss: 0.9931 - digit_4_loss: 1.2390 - digit_1_acc: 1.0000 - digit_2_acc: 0.8895 - digit_3_acc: 0.6667 - digit_4_acc: 0.6240 - val_loss: 7.8602 - val_digit_1_loss: 1.5979e-05 - val_digit_2_loss: 0.6383 - val_digit_3_loss: 1.4468 - val_digit_4_loss: 2.1338 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7965 - val_digit_3_acc: 0.5058 - val_digit_4_acc: 0.3140\n", "Epoch 13/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 5.9985 - digit_1_loss: 1.0849e-06 - digit_2_loss: 0.3453 - digit_3_loss: 0.9335 - digit_4_loss: 1.1416 - digit_1_acc: 1.0000 - digit_2_acc: 0.8915 - digit_3_acc: 0.7112 - digit_4_acc: 0.6240 - val_loss: 7.6809 - val_digit_1_loss: 7.1409e-06 - val_digit_2_loss: 0.6173 - val_digit_3_loss: 1.4998 - val_digit_4_loss: 2.0693 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.5465 - val_digit_4_acc: 0.3023\n", "Epoch 14/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 5.6558 - digit_1_loss: 1.1205e-06 - digit_2_loss: 0.2897 - digit_3_loss: 0.8579 - digit_4_loss: 1.0729 - digit_1_acc: 1.0000 - digit_2_acc: 0.8992 - digit_3_acc: 0.7345 - digit_4_acc: 0.6531 - val_loss: 7.7670 - val_digit_1_loss: 1.3306e-05 - val_digit_2_loss: 0.6206 - val_digit_3_loss: 1.5397 - val_digit_4_loss: 2.2493 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8081 - val_digit_3_acc: 0.5058 - val_digit_4_acc: 0.3081\n", "Epoch 15/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 5.3927 - digit_1_loss: 1.1463e-06 - digit_2_loss: 0.2754 - digit_3_loss: 0.7918 - digit_4_loss: 1.0230 - digit_1_acc: 1.0000 - digit_2_acc: 0.9109 - digit_3_acc: 0.7345 - digit_4_acc: 0.6550 - val_loss: 7.3198 - val_digit_1_loss: 1.5880e-05 - val_digit_2_loss: 0.6336 - val_digit_3_loss: 1.5021 - val_digit_4_loss: 1.9545 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.4942 - val_digit_4_acc: 0.3779\n", "Epoch 16/50\n", "516/516 [==============================] - 0s 963us/step - loss: 5.0022 - digit_1_loss: 1.2697e-06 - digit_2_loss: 0.2458 - digit_3_loss: 0.6689 - digit_4_loss: 0.9090 - digit_1_acc: 1.0000 - digit_2_acc: 0.9225 - digit_3_acc: 0.8023 - digit_4_acc: 0.6996 - val_loss: 7.1431 - val_digit_1_loss: 2.0070e-05 - val_digit_2_loss: 0.6911 - val_digit_3_loss: 1.4475 - val_digit_4_loss: 1.8941 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8081 - val_digit_3_acc: 0.5058 - val_digit_4_acc: 0.3895\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 17/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 4.8441 - digit_1_loss: 1.3004e-06 - digit_2_loss: 0.2104 - digit_3_loss: 0.6459 - digit_4_loss: 0.9257 - digit_1_acc: 1.0000 - digit_2_acc: 0.9360 - digit_3_acc: 0.7984 - digit_4_acc: 0.7054 - val_loss: 6.9537 - val_digit_1_loss: 1.1224e-05 - val_digit_2_loss: 0.6151 - val_digit_3_loss: 1.3407 - val_digit_4_loss: 2.0000 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8198 - val_digit_3_acc: 0.5407 - val_digit_4_acc: 0.3547\n", "Epoch 18/50\n", "516/516 [==============================] - 1s 979us/step - loss: 4.5550 - digit_1_loss: 1.4268e-06 - digit_2_loss: 0.2025 - digit_3_loss: 0.5901 - digit_4_loss: 0.8097 - digit_1_acc: 1.0000 - digit_2_acc: 0.9244 - digit_3_acc: 0.8062 - digit_4_acc: 0.7345 - val_loss: 6.9456 - val_digit_1_loss: 7.0148e-06 - val_digit_2_loss: 0.6274 - val_digit_3_loss: 1.3633 - val_digit_4_loss: 2.0630 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8314 - val_digit_3_acc: 0.5698 - val_digit_4_acc: 0.3663\n", "Epoch 19/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 4.4110 - digit_1_loss: 1.4370e-06 - digit_2_loss: 0.1708 - digit_3_loss: 0.5609 - digit_4_loss: 0.8301 - digit_1_acc: 1.0000 - digit_2_acc: 0.9477 - digit_3_acc: 0.8295 - digit_4_acc: 0.7209 - val_loss: 6.8340 - val_digit_1_loss: 5.7747e-06 - val_digit_2_loss: 0.6275 - val_digit_3_loss: 1.3504 - val_digit_4_loss: 2.0627 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.5407 - val_digit_4_acc: 0.3314\n", "Epoch 20/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 4.0422 - digit_1_loss: 1.3224e-06 - digit_2_loss: 0.1778 - digit_3_loss: 0.4738 - digit_4_loss: 0.6353 - digit_1_acc: 1.0000 - digit_2_acc: 0.9302 - digit_3_acc: 0.8624 - digit_4_acc: 0.8140 - val_loss: 7.2158 - val_digit_1_loss: 3.7410e-06 - val_digit_2_loss: 0.7746 - val_digit_3_loss: 1.4701 - val_digit_4_loss: 2.2662 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7733 - val_digit_3_acc: 0.4942 - val_digit_4_acc: 0.3081\n", "Epoch 21/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 3.9792 - digit_1_loss: 1.3492e-06 - digit_2_loss: 0.1788 - digit_3_loss: 0.4291 - digit_4_loss: 0.7021 - digit_1_acc: 1.0000 - digit_2_acc: 0.9341 - digit_3_acc: 0.8876 - digit_4_acc: 0.7771 - val_loss: 6.7440 - val_digit_1_loss: 5.6063e-06 - val_digit_2_loss: 0.5966 - val_digit_3_loss: 1.4051 - val_digit_4_loss: 2.1194 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8314 - val_digit_3_acc: 0.5988 - val_digit_4_acc: 0.3372\n", "Epoch 22/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 3.6120 - digit_1_loss: 1.5853e-06 - digit_2_loss: 0.1191 - digit_3_loss: 0.3711 - digit_4_loss: 0.5313 - digit_1_acc: 1.0000 - digit_2_acc: 0.9632 - digit_3_acc: 0.8973 - digit_4_acc: 0.8450 - val_loss: 6.5037 - val_digit_1_loss: 4.3639e-06 - val_digit_2_loss: 0.6276 - val_digit_3_loss: 1.3601 - val_digit_4_loss: 1.9691 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8023 - val_digit_3_acc: 0.5407 - val_digit_4_acc: 0.3779\n", "Epoch 23/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 3.4790 - digit_1_loss: 1.4596e-06 - digit_2_loss: 0.1195 - digit_3_loss: 0.3512 - digit_4_loss: 0.4936 - digit_1_acc: 1.0000 - digit_2_acc: 0.9671 - digit_3_acc: 0.9070 - digit_4_acc: 0.8624 - val_loss: 6.6657 - val_digit_1_loss: 2.5962e-06 - val_digit_2_loss: 0.6298 - val_digit_3_loss: 1.4125 - val_digit_4_loss: 2.1530 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.5523 - val_digit_4_acc: 0.3140\n", "Epoch 24/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 3.3087 - digit_1_loss: 1.5686e-06 - digit_2_loss: 0.1051 - digit_3_loss: 0.3225 - digit_4_loss: 0.4433 - digit_1_acc: 1.0000 - digit_2_acc: 0.9729 - digit_3_acc: 0.9167 - digit_4_acc: 0.8798 - val_loss: 6.5365 - val_digit_1_loss: 2.4118e-06 - val_digit_2_loss: 0.7079 - val_digit_3_loss: 1.3729 - val_digit_4_loss: 2.0620 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8140 - val_digit_3_acc: 0.5581 - val_digit_4_acc: 0.3430\n", "Epoch 25/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 3.1817 - digit_1_loss: 1.5448e-06 - digit_2_loss: 0.1027 - digit_3_loss: 0.3039 - digit_4_loss: 0.4133 - digit_1_acc: 1.0000 - digit_2_acc: 0.9671 - digit_3_acc: 0.9225 - digit_4_acc: 0.8915 - val_loss: 6.5433 - val_digit_1_loss: 2.5075e-06 - val_digit_2_loss: 0.6183 - val_digit_3_loss: 1.3754 - val_digit_4_loss: 2.2304 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8372 - val_digit_3_acc: 0.5814 - val_digit_4_acc: 0.3372\n", "Epoch 26/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 3.0590 - digit_1_loss: 1.3416e-06 - digit_2_loss: 0.0982 - digit_3_loss: 0.2706 - digit_4_loss: 0.4013 - digit_1_acc: 1.0000 - digit_2_acc: 0.9690 - digit_3_acc: 0.9419 - digit_4_acc: 0.9050 - val_loss: 6.3684 - val_digit_1_loss: 2.8755e-06 - val_digit_2_loss: 0.6020 - val_digit_3_loss: 1.3781 - val_digit_4_loss: 2.1398 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.5930 - val_digit_4_acc: 0.2907\n", "Epoch 27/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.9256 - digit_1_loss: 1.4642e-06 - digit_2_loss: 0.0737 - digit_3_loss: 0.2668 - digit_4_loss: 0.3648 - digit_1_acc: 1.0000 - digit_2_acc: 0.9864 - digit_3_acc: 0.9380 - digit_4_acc: 0.9089 - val_loss: 6.0638 - val_digit_1_loss: 3.5622e-06 - val_digit_2_loss: 0.5658 - val_digit_3_loss: 1.2401 - val_digit_4_loss: 2.0744 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8314 - val_digit_3_acc: 0.6279 - val_digit_4_acc: 0.3663\n", "Epoch 28/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.8089 - digit_1_loss: 1.6869e-06 - digit_2_loss: 0.0705 - digit_3_loss: 0.2322 - digit_4_loss: 0.3478 - digit_1_acc: 1.0000 - digit_2_acc: 0.9845 - digit_3_acc: 0.9419 - digit_4_acc: 0.9012 - val_loss: 6.0693 - val_digit_1_loss: 3.4756e-06 - val_digit_2_loss: 0.5866 - val_digit_3_loss: 1.2312 - val_digit_4_loss: 2.1269 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.6337 - val_digit_4_acc: 0.3605\n", "Epoch 29/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.7146 - digit_1_loss: 1.6236e-06 - digit_2_loss: 0.0791 - digit_3_loss: 0.2014 - digit_4_loss: 0.3349 - digit_1_acc: 1.0000 - digit_2_acc: 0.9787 - digit_3_acc: 0.9593 - digit_4_acc: 0.9225 - val_loss: 6.1502 - val_digit_1_loss: 2.8692e-06 - val_digit_2_loss: 0.6107 - val_digit_3_loss: 1.3569 - val_digit_4_loss: 2.1176 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8372 - val_digit_3_acc: 0.5872 - val_digit_4_acc: 0.3198\n", "Epoch 30/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.5999 - digit_1_loss: 1.3356e-06 - digit_2_loss: 0.0695 - digit_3_loss: 0.1884 - digit_4_loss: 0.3009 - digit_1_acc: 1.0000 - digit_2_acc: 0.9787 - digit_3_acc: 0.9651 - digit_4_acc: 0.9360 - val_loss: 5.8657 - val_digit_1_loss: 3.0903e-06 - val_digit_2_loss: 0.5962 - val_digit_3_loss: 1.2812 - val_digit_4_loss: 1.9799 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8488 - val_digit_3_acc: 0.6395 - val_digit_4_acc: 0.4070\n", "Epoch 31/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.5580 - digit_1_loss: 1.3319e-06 - digit_2_loss: 0.0692 - digit_3_loss: 0.1995 - digit_4_loss: 0.3048 - digit_1_acc: 1.0000 - digit_2_acc: 0.9806 - digit_3_acc: 0.9516 - digit_4_acc: 0.9380 - val_loss: 5.9396 - val_digit_1_loss: 2.6468e-06 - val_digit_2_loss: 0.5971 - val_digit_3_loss: 1.4018 - val_digit_4_loss: 1.9888 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8488 - val_digit_3_acc: 0.5988 - val_digit_4_acc: 0.3663\n", "Epoch 32/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.4187 - digit_1_loss: 1.3933e-06 - digit_2_loss: 0.0598 - digit_3_loss: 0.1666 - digit_4_loss: 0.2641 - digit_1_acc: 1.0000 - digit_2_acc: 0.9806 - digit_3_acc: 0.9671 - digit_4_acc: 0.9496 - val_loss: 5.7042 - val_digit_1_loss: 2.5137e-06 - val_digit_2_loss: 0.6182 - val_digit_3_loss: 1.2594 - val_digit_4_loss: 1.9305 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8430 - val_digit_3_acc: 0.6221 - val_digit_4_acc: 0.4360\n", "Epoch 33/50\n"]}, {"name": "stdout", "output_type": "stream", "text": ["516/516 [==============================] - 1s 1ms/step - loss: 2.3800 - digit_1_loss: 1.3039e-06 - digit_2_loss: 0.0509 - digit_3_loss: 0.1572 - digit_4_loss: 0.2973 - digit_1_acc: 1.0000 - digit_2_acc: 0.9942 - digit_3_acc: 0.9709 - digit_4_acc: 0.9225 - val_loss: 6.0049 - val_digit_1_loss: 2.2878e-06 - val_digit_2_loss: 0.6312 - val_digit_3_loss: 1.3758 - val_digit_4_loss: 2.1521 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.5872 - val_digit_4_acc: 0.3372\n", "Epoch 34/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.2881 - digit_1_loss: 1.4453e-06 - digit_2_loss: 0.0505 - digit_3_loss: 0.1493 - digit_4_loss: 0.2634 - digit_1_acc: 1.0000 - digit_2_acc: 0.9903 - digit_3_acc: 0.9767 - digit_4_acc: 0.9438 - val_loss: 6.2815 - val_digit_1_loss: 2.5477e-06 - val_digit_2_loss: 0.6468 - val_digit_3_loss: 1.3832 - val_digit_4_loss: 2.4549 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8081 - val_digit_3_acc: 0.5814 - val_digit_4_acc: 0.2965\n", "Epoch 35/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.2406 - digit_1_loss: 1.5372e-06 - digit_2_loss: 0.0574 - digit_3_loss: 0.1588 - digit_4_loss: 0.2483 - digit_1_acc: 1.0000 - digit_2_acc: 0.9942 - digit_3_acc: 0.9651 - digit_4_acc: 0.9380 - val_loss: 5.8016 - val_digit_1_loss: 3.0612e-06 - val_digit_2_loss: 0.6595 - val_digit_3_loss: 1.2765 - val_digit_4_loss: 2.1164 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8140 - val_digit_3_acc: 0.5988 - val_digit_4_acc: 0.3430\n", "Epoch 36/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.1369 - digit_1_loss: 1.2512e-06 - digit_2_loss: 0.0488 - digit_3_loss: 0.1285 - digit_4_loss: 0.2272 - digit_1_acc: 1.0000 - digit_2_acc: 0.9864 - digit_3_acc: 0.9787 - digit_4_acc: 0.9457 - val_loss: 5.9821 - val_digit_1_loss: 3.1256e-06 - val_digit_2_loss: 0.6488 - val_digit_3_loss: 1.3193 - val_digit_4_loss: 2.3040 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8314 - val_digit_3_acc: 0.6163 - val_digit_4_acc: 0.2849\n", "Epoch 37/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.0576 - digit_1_loss: 1.2066e-06 - digit_2_loss: 0.0452 - digit_3_loss: 0.1259 - digit_4_loss: 0.1938 - digit_1_acc: 1.0000 - digit_2_acc: 0.9922 - digit_3_acc: 0.9729 - digit_4_acc: 0.9554 - val_loss: 5.4383 - val_digit_1_loss: 4.2378e-06 - val_digit_2_loss: 0.5961 - val_digit_3_loss: 1.2576 - val_digit_4_loss: 1.9165 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8430 - val_digit_3_acc: 0.6163 - val_digit_4_acc: 0.4128\n", "Epoch 38/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 2.0166 - digit_1_loss: 1.4229e-06 - digit_2_loss: 0.0443 - digit_3_loss: 0.1333 - digit_4_loss: 0.1878 - digit_1_acc: 1.0000 - digit_2_acc: 0.9903 - digit_3_acc: 0.9806 - digit_4_acc: 0.9651 - val_loss: 6.1744 - val_digit_1_loss: 3.2698e-06 - val_digit_2_loss: 0.5980 - val_digit_3_loss: 1.4320 - val_digit_4_loss: 2.5156 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8547 - val_digit_3_acc: 0.5291 - val_digit_4_acc: 0.2616\n", "Epoch 39/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.9615 - digit_1_loss: 1.5987e-06 - digit_2_loss: 0.0556 - digit_3_loss: 0.1195 - digit_4_loss: 0.1742 - digit_1_acc: 1.0000 - digit_2_acc: 0.9903 - digit_3_acc: 0.9806 - digit_4_acc: 0.9496 - val_loss: 5.9199 - val_digit_1_loss: 3.6800e-06 - val_digit_2_loss: 0.5691 - val_digit_3_loss: 1.3992 - val_digit_4_loss: 2.3620 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8488 - val_digit_3_acc: 0.5756 - val_digit_4_acc: 0.3023\n", "Epoch 40/50\n", "516/516 [==============================] - 0s 938us/step - loss: 1.9057 - digit_1_loss: 2.0636e-06 - digit_2_loss: 0.0490 - digit_3_loss: 0.1131 - digit_4_loss: 0.1709 - digit_1_acc: 1.0000 - digit_2_acc: 0.9884 - digit_3_acc: 0.9748 - digit_4_acc: 0.9690 - val_loss: 5.5489 - val_digit_1_loss: 4.0507e-06 - val_digit_2_loss: 0.6624 - val_digit_3_loss: 1.3039 - val_digit_4_loss: 2.0328 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8140 - val_digit_3_acc: 0.5756 - val_digit_4_acc: 0.4128\n", "Epoch 41/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.8180 - digit_1_loss: 1.4298e-06 - digit_2_loss: 0.0368 - digit_3_loss: 0.0936 - digit_4_loss: 0.1541 - digit_1_acc: 1.0000 - digit_2_acc: 0.9961 - digit_3_acc: 0.9884 - digit_4_acc: 0.9729 - val_loss: 5.5295 - val_digit_1_loss: 3.8768e-06 - val_digit_2_loss: 0.6822 - val_digit_3_loss: 1.2527 - val_digit_4_loss: 2.0829 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.7965 - val_digit_3_acc: 0.6279 - val_digit_4_acc: 0.4012\n", "Epoch 42/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.7572 - digit_1_loss: 1.3778e-06 - digit_2_loss: 0.0329 - digit_3_loss: 0.0900 - digit_4_loss: 0.1385 - digit_1_acc: 1.0000 - digit_2_acc: 0.9942 - digit_3_acc: 0.9922 - digit_4_acc: 0.9787 - val_loss: 5.3476 - val_digit_1_loss: 3.3196e-06 - val_digit_2_loss: 0.6019 - val_digit_3_loss: 1.3577 - val_digit_4_loss: 1.9148 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8372 - val_digit_3_acc: 0.5698 - val_digit_4_acc: 0.4186\n", "Epoch 43/50\n", "516/516 [==============================] - 0s 940us/step - loss: 1.7445 - digit_1_loss: 1.4032e-06 - digit_2_loss: 0.0349 - digit_3_loss: 0.0899 - digit_4_loss: 0.1635 - digit_1_acc: 1.0000 - digit_2_acc: 0.9903 - digit_3_acc: 0.9922 - digit_4_acc: 0.9593 - val_loss: 5.4477 - val_digit_1_loss: 4.0881e-06 - val_digit_2_loss: 0.6967 - val_digit_3_loss: 1.2652 - val_digit_4_loss: 2.0525 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8081 - val_digit_3_acc: 0.6047 - val_digit_4_acc: 0.3837\n", "Epoch 44/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.6870 - digit_1_loss: 1.3559e-06 - digit_2_loss: 0.0331 - digit_3_loss: 0.1019 - digit_4_loss: 0.1353 - digit_1_acc: 1.0000 - digit_2_acc: 0.9961 - digit_3_acc: 0.9845 - digit_4_acc: 0.9806 - val_loss: 5.5106 - val_digit_1_loss: 3.4645e-06 - val_digit_2_loss: 0.5912 - val_digit_3_loss: 1.3156 - val_digit_4_loss: 2.2092 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8372 - val_digit_3_acc: 0.5930 - val_digit_4_acc: 0.3721\n", "Epoch 45/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.6075 - digit_1_loss: 1.5536e-06 - digit_2_loss: 0.0260 - digit_3_loss: 0.0751 - digit_4_loss: 0.1279 - digit_1_acc: 1.0000 - digit_2_acc: 0.9961 - digit_3_acc: 0.9922 - digit_4_acc: 0.9748 - val_loss: 5.4769 - val_digit_1_loss: 3.3841e-06 - val_digit_2_loss: 0.6283 - val_digit_3_loss: 1.3254 - val_digit_4_loss: 2.1665 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.6163 - val_digit_4_acc: 0.3314\n", "Epoch 46/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.5527 - digit_1_loss: 1.3508e-06 - digit_2_loss: 0.0205 - digit_3_loss: 0.0745 - digit_4_loss: 0.1166 - digit_1_acc: 1.0000 - digit_2_acc: 1.0000 - digit_3_acc: 0.9942 - digit_4_acc: 0.9826 - val_loss: 5.2726 - val_digit_1_loss: 4.6154e-06 - val_digit_2_loss: 0.6049 - val_digit_3_loss: 1.3034 - val_digit_4_loss: 2.0441 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8430 - val_digit_3_acc: 0.6105 - val_digit_4_acc: 0.3837\n", "Epoch 47/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.5008 - digit_1_loss: 1.4023e-06 - digit_2_loss: 0.0296 - digit_3_loss: 0.0686 - digit_4_loss: 0.0978 - digit_1_acc: 1.0000 - digit_2_acc: 0.9942 - digit_3_acc: 0.9903 - digit_4_acc: 0.9806 - val_loss: 5.4098 - val_digit_1_loss: 3.8331e-06 - val_digit_2_loss: 0.6622 - val_digit_3_loss: 1.3498 - val_digit_4_loss: 2.1133 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8256 - val_digit_3_acc: 0.5698 - val_digit_4_acc: 0.3314\n", "Epoch 48/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.4763 - digit_1_loss: 1.3053e-06 - digit_2_loss: 0.0251 - digit_3_loss: 0.0668 - digit_4_loss: 0.1143 - digit_1_acc: 1.0000 - digit_2_acc: 0.9961 - digit_3_acc: 0.9922 - digit_4_acc: 0.9767 - val_loss: 5.3683 - val_digit_1_loss: 4.4699e-06 - val_digit_2_loss: 0.6094 - val_digit_3_loss: 1.5111 - val_digit_4_loss: 1.9965 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8372 - val_digit_3_acc: 0.5174 - val_digit_4_acc: 0.3895\n", "Epoch 49/50\n"]}, {"name": "stdout", "output_type": "stream", "text": ["516/516 [==============================] - 1s 1ms/step - loss: 1.4602 - digit_1_loss: 1.5513e-06 - digit_2_loss: 0.0199 - digit_3_loss: 0.0895 - digit_4_loss: 0.1122 - digit_1_acc: 1.0000 - digit_2_acc: 1.0000 - digit_3_acc: 0.9884 - digit_4_acc: 0.9864 - val_loss: 5.2803 - val_digit_1_loss: 4.3175e-06 - val_digit_2_loss: 0.6253 - val_digit_3_loss: 1.2202 - val_digit_4_loss: 2.2127 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8372 - val_digit_3_acc: 0.6337 - val_digit_4_acc: 0.3721\n", "Epoch 50/50\n", "516/516 [==============================] - 1s 1ms/step - loss: 1.4065 - digit_1_loss: 1.4596e-06 - digit_2_loss: 0.0246 - digit_3_loss: 0.0712 - digit_4_loss: 0.1008 - digit_1_acc: 1.0000 - digit_2_acc: 0.9942 - digit_3_acc: 0.9884 - digit_4_acc: 0.9864 - val_loss: 5.3520 - val_digit_1_loss: 3.4977e-06 - val_digit_2_loss: 0.6662 - val_digit_3_loss: 1.4047 - val_digit_4_loss: 2.0884 - val_digit_1_acc: 1.0000 - val_digit_2_acc: 0.8140 - val_digit_3_acc: 0.5523 - val_digit_4_acc: 0.3953\n"]}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.0\n", "exact accuracy 0.005813953488372093\n", "exact accuracy 0.005813953488372093\n", "exact accuracy 0.005813953488372093\n", "exact accuracy 0.005813953488372093\n", "exact accuracy 0.005813953488372093\n", "exact accuracy 0.011627906976744186\n", "exact accuracy 0.01744186046511628\n", "exact accuracy 0.01744186046511628\n", "exact accuracy 0.01744186046511628\n", "exact accuracy 0.01744186046511628\n", "exact accuracy 0.01744186046511628\n", "exact accuracy 0.01744186046511628\n", "exact accuracy 0.01744186046511628\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.023255813953488372\n", "exact accuracy 0.029069767441860465\n", "exact accuracy 0.029069767441860465\n", "exact accuracy 0.029069767441860465\n", "exact accuracy 0.029069767441860465\n", "exact accuracy 0.029069767441860465\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.03488372093023256\n", "exact accuracy 0.040697674418604654\n", "exact accuracy 0.040697674418604654\n", "exact accuracy 0.040697674418604654\n", "exact accuracy 0.040697674418604654\n", "exact accuracy 0.040697674418604654\n", "exact accuracy 0.040697674418604654\n", "exact accuracy 0.040697674418604654\n", "exact accuracy 0.046511627906976744\n", "exact accuracy 0.05232558139534884\n", "exact accuracy 0.05232558139534884\n", "exact accuracy 0.05813953488372093\n", "exact accuracy 0.06395348837209303\n", "exact accuracy 0.06395348837209303\n", "exact accuracy 0.06976744186046512\n", "exact accuracy 0.06976744186046512\n", "exact accuracy 0.06976744186046512\n", "exact accuracy 0.06976744186046512\n", "exact accuracy 0.0755813953488372\n", "exact accuracy 0.08139534883720931\n", "exact accuracy 0.0872093023255814\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09302325581395349\n", "exact accuracy 0.09883720930232558\n", "exact accuracy 0.09883720930232558\n", "exact accuracy 0.09883720930232558\n", "exact accuracy 0.09883720930232558\n", "exact accuracy 0.10465116279069768\n", "exact accuracy 0.11046511627906977\n", "exact accuracy 0.11046511627906977\n", "exact accuracy 0.11627906976744186\n", "exact accuracy 0.11627906976744186\n", "exact accuracy 0.11627906976744186\n", "exact accuracy 0.12209302325581395\n", "exact accuracy 0.12209302325581395\n", "exact accuracy 0.12209302325581395\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.12790697674418605\n", "exact accuracy 0.13372093023255813\n", "exact accuracy 0.13372093023255813\n", "exact accuracy 0.13372093023255813\n", "exact accuracy 0.13372093023255813\n", "exact accuracy 0.13372093023255813\n", "exact accuracy 0.13953488372093023\n", "exact accuracy 0.13953488372093023\n", "exact accuracy 0.13953488372093023\n", "exact accuracy 0.13953488372093023\n", "exact accuracy 0.13953488372093023\n", "exact accuracy 0.14534883720930233\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1511627906976744\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.1569767441860465\n", "exact accuracy 0.16279069767441862\n", "exact accuracy 0.1686046511627907\n", "exact accuracy 0.1744186046511628\n", "exact accuracy 0.18023255813953487\n", "exact accuracy 0.18023255813953487\n", "exact accuracy 0.18023255813953487\n", "exact accuracy 0.18023255813953487\n", "exact accuracy 0.18023255813953487\n", "exact accuracy 0.18023255813953487\n", "exact accuracy 0.18604651162790697\n", "exact accuracy 0.18604651162790697\n", "exact accuracy 0.18604651162790697\n", "exact accuracy 0.18604651162790697\n", "exact accuracy 0.19186046511627908\n", "exact accuracy 0.19186046511627908\n", "exact accuracy 0.19186046511627908\n", "exact accuracy 0.19767441860465115\n", "exact accuracy 0.19767441860465115\n", "exact accuracy 0.20348837209302326\n", "exact accuracy 0.20348837209302326\n", "exact accuracy 0.20348837209302326\n", "exact accuracy 0.20930232558139536\n", "exact accuracy 0.20930232558139536\n", "exact accuracy 0.21511627906976744\n", "exact accuracy 0.21511627906976744\n", "exact accuracy 0.21511627906976744\n", "exact accuracy 0.21511627906976744\n", "exact accuracy 0.21511627906976744\n", "difference label vs. prediction [1, -200, -2, 68, -11, 55, 201, 20, -21, 60, -9, -900, -3, 64, 0, -44, 24, -900, -700, 0, 0, 5, -10, -10, 2, -4, 15, 0, -8, 869, 2, -6, -4, 614, -2, -871, -1, -60, -1, 9, 0, 9, 9, 30, 921, 0, -1, 4, 4, -30, -2, -9, -79, 37, 21, 47, 16, -740, 2, 46, 0, -840, -4, -883, -21, 60, -940, 0, 0, 31, 0, 0, -9, 0, -900, -1, 8, 0, 0, 0, 0, 60, 6, -315, -906, 19, 3, 20, 0, 65, 1, 20, 0, 0, 50, 0, 60, -935, 0, -20, -87, 0, 48, 5, -2, -843, -1, -6, 60, 0, 96, 1, -805, 821, 0, 40, 11, 4, -19, 0, 0, -20, -20, -906, 54, 7, 21, -7, 34, -81, -33, 25, -925, -906, -899, 0, -1, -843, 30, -10, -6, 21, 8, 21, 0, 0, 0, 0, -891, 5, 57, 4, 892, 0, 4, -3, -861, 0, 80, 3, 0, 2, 0, 60, -5, 0, 1, 0, -10, -952, -1, -6]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Seven-Segment-OCR/Model.py:198: UserWarning: Update your `Dense` call to the Keras 2 API: `Dense(activation=\"softmax\", name=\"output\", units=11)`\n", "  output = Dense(output_dim =11,activation = 'softmax', name='output')(x)\n", "/home/<USER>/Seven-Segment-OCR/Model.py:200: UserWarning: Update your `Model` call to the Keras 2 API: `Model(inputs=Tensor(\"in..., outputs=Tensor(\"ou...)`\n", "  self.model = keras.models.Model(input = model_input , output = output)\n", "/home/<USER>/Seven-Segment-OCR/Model.py:207: UserWarning: The `nb_epoch` argument in `fit` has been renamed `epochs`.\n", "  self.history = self.model.fit(self.X_train, self.y_train, batch_size= 32, nb_epoch=30, verbose=1, validation_data=(self.X_val, self.y_val))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Train on 2064 samples, validate on 688 samples\n", "Epoch 1/30\n", "2064/2064 [==============================] - 7s 3ms/step - loss: 18.6020 - acc: 0.3953 - val_loss: 12.9582 - val_acc: 0.4084\n", "Epoch 2/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 11.5059 - acc: 0.4016 - val_loss: 10.2261 - val_acc: 0.4084\n", "Epoch 3/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 8.0329 - acc: 0.3915 - val_loss: 6.0187 - val_acc: 0.2907\n", "Epoch 4/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 2.6648 - acc: 0.4520 - val_loss: 2.3791 - val_acc: 0.4564\n", "Epoch 5/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 2.0756 - acc: 0.5586 - val_loss: 2.0950 - val_acc: 0.5160\n", "Epoch 6/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.7416 - acc: 0.6323 - val_loss: 1.9161 - val_acc: 0.5814\n", "Epoch 7/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.5996 - acc: 0.6662 - val_loss: 1.6231 - val_acc: 0.6279\n", "Epoch 8/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.4174 - acc: 0.7040 - val_loss: 1.5471 - val_acc: 0.6483\n", "Epoch 9/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.3284 - acc: 0.7166 - val_loss: 1.4834 - val_acc: 0.6802\n", "Epoch 10/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.2886 - acc: 0.7248 - val_loss: 1.3061 - val_acc: 0.7369\n", "Epoch 11/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.2240 - acc: 0.7524 - val_loss: 1.2535 - val_acc: 0.7442\n", "Epoch 12/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.1711 - acc: 0.7529 - val_loss: 1.3851 - val_acc: 0.6831\n", "Epoch 13/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.1794 - acc: 0.7573 - val_loss: 1.2351 - val_acc: 0.7573\n", "Epoch 14/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.1661 - acc: 0.7505 - val_loss: 1.2331 - val_acc: 0.7384\n", "Epoch 15/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.1294 - acc: 0.7607 - val_loss: 1.3475 - val_acc: 0.7035\n", "Epoch 16/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.1237 - acc: 0.7689 - val_loss: 1.2531 - val_acc: 0.7529\n", "Epoch 17/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0853 - acc: 0.7796 - val_loss: 1.2607 - val_acc: 0.7413\n", "Epoch 18/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0879 - acc: 0.7766 - val_loss: 1.1968 - val_acc: 0.7529\n", "Epoch 19/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0821 - acc: 0.7776 - val_loss: 1.5113 - val_acc: 0.6933\n", "Epoch 20/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0901 - acc: 0.7897 - val_loss: 1.2322 - val_acc: 0.7573\n", "Epoch 21/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0461 - acc: 0.7926 - val_loss: 1.2671 - val_acc: 0.7369\n", "Epoch 22/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0444 - acc: 0.7897 - val_loss: 1.3538 - val_acc: 0.7209\n", "Epoch 23/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0445 - acc: 0.7951 - val_loss: 1.2873 - val_acc: 0.7485\n", "Epoch 24/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 1.0483 - acc: 0.7970 - val_loss: 1.2014 - val_acc: 0.7733\n", "Epoch 25/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 0.9891 - acc: 0.8096 - val_loss: 1.2766 - val_acc: 0.7253\n", "Epoch 26/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 0.9911 - acc: 0.8130 - val_loss: 1.2209 - val_acc: 0.7674\n", "Epoch 27/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 0.9701 - acc: 0.8183 - val_loss: 1.2232 - val_acc: 0.7674\n", "Epoch 28/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 0.9810 - acc: 0.8246 - val_loss: 1.2355 - val_acc: 0.7718\n", "Epoch 29/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 0.9823 - acc: 0.8222 - val_loss: 1.2908 - val_acc: 0.7427\n", "Epoch 30/30\n", "2064/2064 [==============================] - 2s 1ms/step - loss: 0.9240 - acc: 0.8348 - val_loss: 1.3209 - val_acc: 0.7485\n"]}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["real_acc 0.748546511627907\n"]}], "source": ["from keras.backend.tensorflow_backend import set_session\n", "from Model import Model_Multi, Model_Single\n", "import tensorflow as tf\n", "\n", "session_config = tf.ConfigProto()\n", "session_config.gpu_options.visible_device_list = \"0\"\n", "session_config.gpu_options.allow_growth = True\n", "set_session(tf.Session(config=session_config))\n", "\n", "model_1 = Model_Multi()\n", "model_1.train_predict()\n", "\n", "model_2 = Model_Single()\n", "model_2.train_predict()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}