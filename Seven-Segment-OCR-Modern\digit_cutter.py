"""
Modern Digit Cutting for Seven-Segment OCR
Updated with improved segmentation and error handling
"""

import cv2
import os
import pandas as pd
import shutil
import numpy as np
from typing import List, Optional, Tuple
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModernDigitCutter:
    """
    Modern digit cutter with improved segmentation algorithms
    and robust error handling.
    """
    
    def __init__(self, 
                 image: Optional[np.ndarray] = None,
                 src_file_name: Optional[str] = None,
                 dst_folder_name: str = 'Datasets_digits',
                 num_digits: int = 4,
                 labels: Optional[List] = None):
        """
        Initialize digit cutter.
        
        Args:
            image: Preprocessed frame image
            src_file_name: Source image filename
            dst_folder_name: Destination folder for digit images
            num_digits: Number of digits to extract
            labels: List of digit labels
        """
        self.src_file_name = src_file_name
        self.dst_folder_name = dst_folder_name
        self.num_digits = num_digits
        self.labels = labels
        
        # Load image
        if image is not None:
            self.image = image
        elif src_file_name is not None:
            self.image = self._load_image(src_file_name)
        else:
            raise ValueError("Either image array or src_file_name must be provided")
        
        # Processing results
        self.digit_boxes = []
        self.box_coordinates = []
        
    def _load_image(self, filepath: str) -> np.ndarray:
        """Load image with error handling."""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Image file not found: {filepath}")
        
        image = cv2.imread(filepath)
        if image is None:
            raise ValueError(f"Could not load image: {filepath}")
        
        return image
    
    def extract_digits_simple(self) -> bool:
        """
        Extract digits using simple equal division method.
        
        Returns:
            True if extraction successful
        """
        try:
            if len(self.image.shape) == 3:
                height, width, _ = self.image.shape
            else:
                height, width = self.image.shape
            
            # Calculate box width
            box_width = width // self.num_digits
            
            self.digit_boxes = []
            self.box_coordinates = []
            
            for i in range(self.num_digits):
                # Calculate boundaries
                left = i * box_width
                right = (i + 1) * box_width
                
                # Extract digit region
                if len(self.image.shape) == 3:
                    digit_box = self.image[:, left:right, :]
                else:
                    digit_box = self.image[:, left:right]
                
                self.digit_boxes.append(digit_box)
                self.box_coordinates.append((left, 0, right, height))
            
            logger.debug(f"Extracted {len(self.digit_boxes)} digits using simple method")
            return True
            
        except Exception as e:
            logger.error(f"Simple digit extraction failed: {e}")
            return False
    
    def extract_digits_adaptive(self) -> bool:
        """
        Extract digits using adaptive segmentation based on content analysis.
        
        Returns:
            True if extraction successful
        """
        try:
            # Convert to grayscale if needed
            if len(self.image.shape) == 3:
                gray = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)
            else:
                gray = self.image.copy()
            
            # Apply binary threshold
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Calculate vertical projection
            vertical_projection = np.sum(binary, axis=0)
            
            # Find digit boundaries using projection analysis
            boundaries = self._find_digit_boundaries(vertical_projection)
            
            if len(boundaries) < self.num_digits + 1:
                logger.warning("Adaptive segmentation failed, falling back to simple method")
                return self.extract_digits_simple()
            
            # Extract digits based on boundaries
            self.digit_boxes = []
            self.box_coordinates = []
            
            for i in range(self.num_digits):
                left = boundaries[i]
                right = boundaries[i + 1]
                
                # Add small padding
                padding = 2
                left = max(0, left - padding)
                right = min(self.image.shape[1], right + padding)
                
                # Extract digit region
                if len(self.image.shape) == 3:
                    digit_box = self.image[:, left:right, :]
                else:
                    digit_box = self.image[:, left:right]
                
                self.digit_boxes.append(digit_box)
                self.box_coordinates.append((left, 0, right, self.image.shape[0]))
            
            logger.debug(f"Extracted {len(self.digit_boxes)} digits using adaptive method")
            return True
            
        except Exception as e:
            logger.error(f"Adaptive digit extraction failed: {e}")
            return self.extract_digits_simple()
    
    def _find_digit_boundaries(self, projection: np.ndarray) -> List[int]:
        """
        Find digit boundaries from vertical projection.
        
        Args:
            projection: Vertical projection of the image
            
        Returns:
            List of boundary positions
        """
        # Smooth projection to reduce noise
        from scipy import ndimage
        smoothed = ndimage.gaussian_filter1d(projection.astype(float), sigma=1.0)
        
        # Find valleys (low values) in projection
        threshold = np.mean(smoothed) * 0.3
        valleys = []
        
        for i in range(1, len(smoothed) - 1):
            if (smoothed[i] < threshold and 
                smoothed[i] < smoothed[i-1] and 
                smoothed[i] < smoothed[i+1]):
                valleys.append(i)
        
        # Select best valleys as boundaries
        if len(valleys) >= self.num_digits - 1:
            # Use k-means-like approach to select optimal boundaries
            boundaries = [0]  # Start boundary
            
            # Divide image into equal segments and find closest valley
            width = len(projection)
            for i in range(1, self.num_digits):
                target_pos = (i * width) // self.num_digits
                
                # Find closest valley to target position
                closest_valley = min(valleys, key=lambda x: abs(x - target_pos))
                boundaries.append(closest_valley)
                valleys.remove(closest_valley)  # Avoid reusing
            
            boundaries.append(width - 1)  # End boundary
            boundaries.sort()
            
        else:
            # Fallback to equal division
            width = len(projection)
            boundaries = [i * width // self.num_digits for i in range(self.num_digits + 1)]
        
        return boundaries
    
    def preprocess_digits(self, target_size: Tuple[int, int] = (64, 32)) -> None:
        """
        Preprocess extracted digits for better recognition.
        
        Args:
            target_size: Target size for digit images (height, width)
        """
        if not self.digit_boxes:
            raise ValueError("Digits must be extracted before preprocessing")
        
        processed_digits = []
        
        for digit_box in self.digit_boxes:
            try:
                # Convert to grayscale if needed
                if len(digit_box.shape) == 3:
                    gray_digit = cv2.cvtColor(digit_box, cv2.COLOR_BGR2GRAY)
                else:
                    gray_digit = digit_box.copy()
                
                # Resize to target size
                resized = cv2.resize(gray_digit, (target_size[1], target_size[0]))
                
                # Apply additional preprocessing
                # Histogram equalization
                equalized = cv2.equalizeHist(resized)
                
                # Gaussian blur to reduce noise
                blurred = cv2.GaussianBlur(equalized, (3, 3), 0)
                
                processed_digits.append(blurred)
                
            except Exception as e:
                logger.error(f"Digit preprocessing failed: {e}")
                # Use original digit as fallback
                processed_digits.append(digit_box)
        
        self.digit_boxes = processed_digits
    
    def save_digits(self) -> bool:
        """
        Save extracted digits to folders.
        
        Returns:
            True if saving successful
        """
        if not self.digit_boxes:
            logger.error("No digits to save")
            return False
        
        if not self.labels:
            logger.warning("No labels provided, skipping save")
            return False
        
        try:
            # Ensure destination folders exist
            for label in range(11):  # 0-9 + unknown (10)
                folder_path = os.path.join(self.dst_folder_name, str(label))
                os.makedirs(folder_path, exist_ok=True)
            
            # Save each digit
            base_filename = Path(self.src_file_name).stem if self.src_file_name else "unknown"
            
            for i, (digit_box, label) in enumerate(zip(self.digit_boxes, self.labels)):
                if label is None or label == 'X':
                    continue  # Skip unlabeled digits
                
                # Create filename
                filename = f"{base_filename}_digit_{i}.jpg"
                filepath = os.path.join(self.dst_folder_name, str(label), filename)
                
                # Save digit image
                success = cv2.imwrite(filepath, digit_box)
                
                if not success:
                    logger.error(f"Failed to save digit to {filepath}")
                    return False
                
                logger.debug(f"Saved digit {label} to {filepath}")
            
            return True
            
        except Exception as e:
            logger.error(f"Digit saving failed: {e}")
            return False
    
    def extract_and_save_digits(self, use_adaptive: bool = True, 
                               preprocess: bool = True) -> bool:
        """
        Complete digit extraction and saving pipeline.
        
        Args:
            use_adaptive: Whether to use adaptive segmentation
            preprocess: Whether to preprocess digits
            
        Returns:
            True if pipeline successful
        """
        try:
            # Extract digits
            if use_adaptive:
                success = self.extract_digits_adaptive()
            else:
                success = self.extract_digits_simple()
            
            if not success:
                return False
            
            # Preprocess if requested
            if preprocess:
                self.preprocess_digits()
            
            # Save digits
            return self.save_digits()
            
        except Exception as e:
            logger.error(f"Digit extraction pipeline failed: {e}")
            return False


def batch_cut_digits(data_dir: str = 'Datasets', 
                    frame_dir: str = 'Datasets_frames',
                    output_dir: str = 'Datasets_digits') -> dict:
    """
    Cut digits from all frames in batch.
    
    Args:
        data_dir: Directory containing CSV label files
        frame_dir: Directory containing frame images
        output_dir: Output directory for digit images
        
    Returns:
        Dictionary with processing statistics
    """
    # Setup output directory
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    
    for i in range(11):  # Create folders for digits 0-10
        os.makedirs(os.path.join(output_dir, str(i)), exist_ok=True)
    
    # Load label data
    try:
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        dataframes = []
        
        for csv_file in csv_files:
            df = pd.read_csv(os.path.join(data_dir, csv_file), sep=';', index_col=0)
            dataframes.append(df)
        
        combined_data = pd.concat(dataframes, axis=0, ignore_index=True)
        combined_data = combined_data.replace("X", 10)
        
    except Exception as e:
        logger.error(f"Failed to load label data: {e}")
        return {'total': 0, 'success': 0, 'failed': 0}
    
    # Process each image
    stats = {'total': len(combined_data), 'success': 0, 'failed': 0}
    
    logger.info(f"Processing {stats['total']} images for digit cutting...")
    
    for idx, row in combined_data.iterrows():
        try:
            image_name = row['image']
            labels = [row['cadran_1'], row['cadran_2'], row['cadran_3'], row['cadran_4']]
            
            src_file = os.path.join(frame_dir, image_name)
            
            if not os.path.exists(src_file):
                logger.warning(f"Frame image not found: {src_file}")
                stats['failed'] += 1
                continue
            
            # Create digit cutter
            cutter = ModernDigitCutter(
                src_file_name=src_file,
                dst_folder_name=output_dir,
                labels=labels
            )
            
            # Extract and save digits
            success = cutter.extract_and_save_digits()
            
            if success:
                stats['success'] += 1
            else:
                stats['failed'] += 1
                
        except Exception as e:
            logger.error(f"Failed to process image {idx}: {e}")
            stats['failed'] += 1
    
    logger.info(f"Digit cutting complete: {stats['success']} success, {stats['failed']} failed")
    return stats


# Main execution
if __name__ == "__main__":
    stats = batch_cut_digits()
    logger.info(f"Final results: {stats}")
    
    success_rate = (stats['success'] / stats['total']) * 100 if stats['total'] > 0 else 0
    logger.info(f"Success rate: {success_rate:.1f}%")
