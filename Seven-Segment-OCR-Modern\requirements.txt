# Modern Dependencies for Seven-Segment OCR
# Compatible with Google Colab and modern Python environments

# Core ML/DL frameworks
tensorflow>=2.12.0
keras>=2.12.0
numpy>=1.21.0
pandas>=1.5.0
scikit-learn>=1.2.0
scipy>=1.9.0

# Computer Vision
opencv-python>=4.7.0
scikit-image>=0.19.0
Pillow>=9.0.0
imutils>=0.5.4

# Visualization and plotting
matplotlib>=3.6.0
seaborn>=0.11.0

# Jupyter/Colab specific
ipywidgets>=8.0.0
tqdm>=4.64.0

# Additional utilities
h5py>=3.7.0
pyyaml>=6.0

# Optional: For enhanced performance
# albumentations>=1.3.0  # Advanced data augmentation
# tensorboard>=2.12.0    # Training visualization
