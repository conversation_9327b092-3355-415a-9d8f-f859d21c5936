"""
Setup script for Seven-Segment OCR Modern
Automated installation and configuration for Google Colab and local environments
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SevenSegmentOCRSetup:
    """
    Automated setup class for Seven-Segment OCR project.
    """
    
    def __init__(self, environment: str = 'auto'):
        """
        Initialize setup.
        
        Args:
            environment: 'colab', 'local', or 'auto' for automatic detection
        """
        self.environment = self._detect_environment() if environment == 'auto' else environment
        self.project_root = Path.cwd()
        self.requirements_installed = False
        
        logger.info(f"Detected environment: {self.environment}")
    
    def _detect_environment(self) -> str:
        """Detect if running in Google Colab or local environment."""
        try:
            import google.colab
            return 'colab'
        except ImportError:
            return 'local'
    
    def install_requirements(self, requirements_file: str = 'requirements.txt') -> bool:
        """
        Install required packages.
        
        Args:
            requirements_file: Path to requirements file
            
        Returns:
            True if installation successful
        """
        try:
            requirements_path = self.project_root / requirements_file
            
            if not requirements_path.exists():
                logger.error(f"Requirements file not found: {requirements_path}")
                return False
            
            logger.info("Installing required packages...")
            
            # Install packages
            if self.environment == 'colab':
                # Use pip with quiet flag for Colab
                cmd = [sys.executable, '-m', 'pip', 'install', '-q', '-r', str(requirements_path)]
            else:
                # Standard pip install for local
                cmd = [sys.executable, '-m', 'pip', 'install', '-r', str(requirements_path)]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Requirements installed successfully!")
                self.requirements_installed = True
                return True
            else:
                logger.error(f"❌ Requirements installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error installing requirements: {e}")
            return False
    
    def setup_directories(self) -> bool:
        """
        Create necessary directories for the project.
        
        Returns:
            True if setup successful
        """
        try:
            directories = [
                'Datasets',
                'Datasets_frames', 
                'Datasets_digits',
                'models',
                'logs',
                'outputs',
                'temp'
            ]
            
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(exist_ok=True)
                logger.info(f"📁 Created directory: {directory}")
            
            # Create subdirectories for digits (0-10)
            digits_dir = self.project_root / 'Datasets_digits'
            for i in range(11):
                (digits_dir / str(i)).mkdir(exist_ok=True)
            
            logger.info("✅ Directory structure created successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error creating directories: {e}")
            return False
    
    def setup_gpu(self) -> Dict[str, any]:
        """
        Setup GPU configuration for optimal performance.
        
        Returns:
            Dictionary with GPU information
        """
        try:
            import tensorflow as tf
            
            # Get GPU information
            gpus = tf.config.experimental.list_physical_devices('GPU')
            gpu_info = {
                'available': len(gpus) > 0,
                'count': len(gpus),
                'devices': [gpu.name for gpu in gpus]
            }
            
            if gpus:
                try:
                    # Enable memory growth
                    for gpu in gpus:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    
                    logger.info(f"✅ GPU setup complete: {len(gpus)} GPU(s) available")
                    gpu_info['memory_growth_enabled'] = True
                    
                except RuntimeError as e:
                    logger.warning(f"⚠️ GPU memory growth setup failed: {e}")
                    gpu_info['memory_growth_enabled'] = False
            else:
                logger.info("ℹ️ No GPUs found, will use CPU")
            
            return gpu_info
            
        except ImportError:
            logger.warning("⚠️ TensorFlow not available for GPU setup")
            return {'available': False, 'count': 0, 'devices': []}
        except Exception as e:
            logger.error(f"❌ Error setting up GPU: {e}")
            return {'available': False, 'count': 0, 'devices': [], 'error': str(e)}
    
    def download_sample_data(self) -> bool:
        """
        Download sample data for testing (if available).
        
        Returns:
            True if download successful
        """
        try:
            # This would download sample data from a repository or cloud storage
            # For now, we'll create some dummy data structure
            
            logger.info("📥 Setting up sample data structure...")
            
            # Create sample CSV file
            import pandas as pd
            
            sample_data = {
                'used_liter': [33, 29, 230],
                'image': ['sample1.jpg', 'sample2.jpg', 'sample3.jpg'],
                'cadran_1': ['X', 'X', 'X'],
                'cadran_2': ['X', 'X', 2],
                'cadran_3': [3, 2, 3],
                'cadran_4': [3, 9, 0]
            }
            
            df = pd.DataFrame(sample_data)
            sample_csv_path = self.project_root / 'Datasets' / 'sample_data.csv'
            df.to_csv(sample_csv_path, sep=';', index_label='index')
            
            logger.info("✅ Sample data structure created!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up sample data: {e}")
            return False
    
    def verify_installation(self) -> Dict[str, bool]:
        """
        Verify that all components are properly installed.
        
        Returns:
            Dictionary with verification results
        """
        results = {}
        
        # Check Python packages
        required_packages = [
            'tensorflow', 'numpy', 'pandas', 'opencv-python', 
            'scikit-learn', 'matplotlib', 'seaborn', 'Pillow'
        ]
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                results[f'package_{package}'] = True
                logger.info(f"✅ {package} is available")
            except ImportError:
                results[f'package_{package}'] = False
                logger.error(f"❌ {package} is not available")
        
        # Check directories
        required_dirs = ['Datasets', 'Datasets_frames', 'Datasets_digits', 'models', 'logs']
        for directory in required_dirs:
            dir_path = self.project_root / directory
            results[f'dir_{directory}'] = dir_path.exists()
            if dir_path.exists():
                logger.info(f"✅ Directory {directory} exists")
            else:
                logger.error(f"❌ Directory {directory} missing")
        
        # Check GPU setup
        try:
            import tensorflow as tf
            gpus = tf.config.experimental.list_physical_devices('GPU')
            results['gpu_available'] = len(gpus) > 0
            if len(gpus) > 0:
                logger.info(f"✅ GPU available: {len(gpus)} device(s)")
            else:
                logger.info("ℹ️ No GPU available, will use CPU")
        except:
            results['gpu_available'] = False
            logger.warning("⚠️ Could not check GPU availability")
        
        return results
    
    def run_complete_setup(self) -> bool:
        """
        Run complete setup process.
        
        Returns:
            True if all setup steps successful
        """
        logger.info("🚀 Starting Seven-Segment OCR setup...")
        
        steps = [
            ("Installing requirements", self.install_requirements),
            ("Setting up directories", self.setup_directories),
            ("Configuring GPU", lambda: self.setup_gpu() is not None),
            ("Setting up sample data", self.download_sample_data)
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            logger.info(f"📋 {step_name}...")
            try:
                if step_func():
                    logger.info(f"✅ {step_name} completed")
                    success_count += 1
                else:
                    logger.error(f"❌ {step_name} failed")
            except Exception as e:
                logger.error(f"❌ {step_name} failed with error: {e}")
        
        # Verify installation
        logger.info("🔍 Verifying installation...")
        verification_results = self.verify_installation()
        
        # Summary
        total_steps = len(steps)
        logger.info(f"\n📊 Setup Summary:")
        logger.info(f"   Completed steps: {success_count}/{total_steps}")
        
        if success_count == total_steps:
            logger.info("🎉 Setup completed successfully!")
            logger.info("You can now start training your Seven-Segment OCR model!")
            return True
        else:
            logger.warning("⚠️ Setup completed with some issues. Check the logs above.")
            return False
    
    def create_colab_setup_cell(self) -> str:
        """
        Create a setup cell for Google Colab.
        
        Returns:
            String containing setup code for Colab
        """
        setup_code = '''
# Seven-Segment OCR - Automated Setup for Google Colab
import os
import sys

# Clone or download the repository (replace with your repository URL)
# !git clone https://github.com/your-username/Seven-Segment-OCR-Modern.git
# os.chdir('Seven-Segment-OCR-Modern')

# Install requirements
!pip install -q tensorflow>=2.12.0 opencv-python>=4.7.0 scikit-image>=0.19.0
!pip install -q imutils>=0.5.4 seaborn>=0.11.0 pandas>=1.5.0 scikit-learn>=1.2.0

# Run setup
from setup import SevenSegmentOCRSetup

setup = SevenSegmentOCRSetup(environment='colab')
setup.run_complete_setup()

print("🎉 Setup complete! You can now run the training notebook.")
'''
        return setup_code


def main():
    """Main setup function."""
    setup = SevenSegmentOCRSetup()
    success = setup.run_complete_setup()
    
    if success:
        print("\n🎉 Seven-Segment OCR setup completed successfully!")
        print("Next steps:")
        print("1. Upload your dataset to the Datasets/ folder")
        print("2. Run the preprocessing scripts to prepare your data")
        print("3. Open the training notebook to start training")
    else:
        print("\n⚠️ Setup completed with issues. Please check the logs and resolve any errors.")
    
    return success


if __name__ == "__main__":
    main()
