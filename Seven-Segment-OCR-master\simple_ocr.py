import os
import numpy as np
import cv2
from PIL import Image
import tensorflow as tf
from Model import Model_Single, Model_Multi

class SimpleOCR:
    """
    Simple OCR interface: provide image path, threshold, get OCR result
    Uses the existing Seven-Segment-OCR codebase
    """
    
    def __init__(self, model_path=None, model_type='single'):
        """
        Initialize the OCR system
        
        Args:
            model_path (str): Path to saved model file (.h5, .keras, etc.)
            model_type (str): 'single' for single digit or 'multi' for multi-digit
        """
        self.model_type = model_type
        self.model = None
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            print("No model loaded. Use load_model() or train_new_model()")
    
    def load_model(self, model_path):
        """Load a pre-trained model"""
        try:
            self.model = tf.keras.models.load_model(model_path)
            print(f"✅ Model loaded from: {model_path}")
            return True
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def train_new_model(self, epochs=20):
        """Train a new model using the existing codebase"""
        try:
            if self.model_type == 'single':
                print("🚀 Training single-digit model...")
                model_trainer = Model_Single()
                model_trainer.train(epochs=epochs)
                self.model = model_trainer.model
                print("✅ Single-digit model trained successfully!")
            else:
                print("🚀 Training multi-digit model...")
                model_trainer = Model_Multi()
                model_trainer.train(epochs=epochs)
                self.model = model_trainer.model
                print("✅ Multi-digit model trained successfully!")
            return True
        except Exception as e:
            print(f"❌ Error training model: {e}")
            return False
    
    def preprocess_image(self, image_path, target_size=(64, 64)):
        """
        Preprocess image for prediction (compatible with Google Colab trained model)

        Args:
            image_path (str): Path to image file
            target_size (tuple): Target size for resizing (64, 64) for Colab model

        Returns:
            np.array: Preprocessed image array
        """
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError(f"Could not load image: {image_path}")

            # Resize to target size (64, 64) to match Colab model
            img = cv2.resize(img, target_size)

            # Keep as RGB (3 channels) to match Colab model input (64, 64, 3)
            if len(img.shape) == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            else:
                # Convert grayscale to RGB
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)

            # Normalize to [0, 1] range
            img = img / 255.0

            # Add batch dimension
            img = np.expand_dims(img, axis=0)

            return img

        except Exception as e:
            print(f"❌ Error preprocessing image: {e}")
            return None
    
    def predict_single_digit(self, image_path, confidence_threshold=0.7):
        """
        Predict single digit from image
        
        Args:
            image_path (str): Path to digit image
            confidence_threshold (float): Minimum confidence threshold
        
        Returns:
            dict: Prediction result
        """
        if self.model is None:
            return {'digit': None, 'confidence': 0.0, 'status': 'no_model_loaded'}
        
        # Preprocess image
        img = self.preprocess_image(image_path)
        if img is None:
            return {'digit': None, 'confidence': 0.0, 'status': 'preprocessing_failed'}
        
        try:
            # Predict
            prediction = self.model.predict(img, verbose=0)
            confidence = float(np.max(prediction))
            digit_class = int(np.argmax(prediction))
            
            # Convert class to digit
            if digit_class == 10:
                digit = "unknown"
            else:
                digit = str(digit_class)
            
            # Check confidence threshold
            if confidence >= confidence_threshold:
                status = "confident"
            else:
                status = "uncertain"
            
            return {
                'digit': digit,
                'confidence': confidence,
                'status': status
            }
            
        except Exception as e:
            return {
                'digit': None,
                'confidence': 0.0,
                'status': f'prediction_error: {str(e)}'
            }
    
    def simple_ocr(self, image_path, confidence_threshold=0.7):
        """
        Main OCR function: Image Path + Threshold → OCR Result
        
        Args:
            image_path (str): Path to the image
            confidence_threshold (float): Minimum confidence to accept prediction
        
        Returns:
            dict: OCR result with digit, confidence, and status
        """
        if self.model_type == 'single':
            return self.predict_single_digit(image_path, confidence_threshold)
        else:
            # For multi-digit, we'll implement this later
            return {'digit': None, 'confidence': 0.0, 'status': 'multi_digit_not_implemented_yet'}
    
    def save_model(self, save_path):
        """Save the current model"""
        if self.model is None:
            print("❌ No model to save")
            return False
        
        try:
            self.model.save(save_path)
            print(f"✅ Model saved to: {save_path}")
            return True
        except Exception as e:
            print(f"❌ Error saving model: {e}")
            return False

# Convenience functions for quick usage
def ocr_digit(image_path, confidence_threshold=0.7, model_path=None):
    """
    Quick OCR function for single digit

    Args:
        image_path (str): Path to digit image
        confidence_threshold (float): Minimum confidence threshold
        model_path (str): Path to model file (optional)

    Returns:
        dict: OCR result
    """
    ocr = SimpleOCR(model_path=model_path, model_type='single')
    return ocr.simple_ocr(image_path, confidence_threshold)

def load_colab_model(model_path):
    """
    Load your Google Colab trained model

    Args:
        model_path (str): Path to your .keras model file from Colab

    Returns:
        SimpleOCR: Configured OCR instance
    """
    print(f"🔄 Loading Google Colab trained model from: {model_path}")
    ocr = SimpleOCR(model_path=model_path, model_type='single')
    if ocr.model is not None:
        print("✅ Colab model loaded successfully!")
        print("📋 Model expects input shape: (64, 64, 3) RGB images")
        print("📋 Model outputs: 11 classes (digits 0-9 + unknown)")
    return ocr

if __name__ == "__main__":
    # Example usage
    print("🔍 Seven-Segment OCR - Simple Interface")
    print("=" * 50)
    
    # Initialize OCR
    ocr = SimpleOCR(model_type='single')
    
    # Check if we have test images
    test_dir = "test"
    if os.path.exists(test_dir):
        test_images = [f for f in os.listdir(test_dir) if f.endswith('.jpg')]
        
        if test_images:
            print(f"📁 Found {len(test_images)} test images")
            
            # Test on first image
            test_image = os.path.join(test_dir, test_images[0])
            print(f"🧪 Testing on: {test_image}")
            
            # Try to load a model first, or train if none exists
            if not ocr.model:
                print("🚀 No model found, training new model...")
                ocr.train_new_model(epochs=5)  # Quick training for demo
            
            # Run OCR
            result = ocr.simple_ocr(test_image, confidence_threshold=0.5)
            print(f"📊 Result: {result}")
        else:
            print("❌ No test images found in test/ directory")
    else:
        print("❌ Test directory not found")
    
    print("\n✅ Simple OCR interface ready!")
    print("Usage: result = ocr.simple_ocr('image.jpg', 0.7)")
