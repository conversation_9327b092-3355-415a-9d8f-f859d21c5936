"""
Modern Model Architectures for Seven-Segment OCR
Compatible with TensorFlow 2.x and optimized for GPU training
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, models, optimizers, callbacks
import numpy as np
from typing import Tuple, Dict, List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BaseOCRModel:
    """Base class for OCR models with common functionality."""
    
    def __init__(self, name: str = "base_ocr_model"):
        """
        Initialize base OCR model.
        
        Args:
            name: Name of the model
        """
        self.name = name
        self.model = None
        self.history = None
        
    def compile_model(self, learning_rate: float = 0.001, 
                     optimizer: str = 'adam') -> None:
        """
        Compile the model with specified optimizer and learning rate.
        
        Args:
            learning_rate: Learning rate for training
            optimizer: Optimizer to use ('adam', 'sgd', 'rmsprop')
        """
        if self.model is None:
            raise ValueError("Model must be built before compilation")
        
        # Select optimizer
        if optimizer.lower() == 'adam':
            opt = optimizers.Adam(learning_rate=learning_rate)
        elif optimizer.lower() == 'sgd':
            opt = optimizers.SGD(learning_rate=learning_rate, momentum=0.9)
        elif optimizer.lower() == 'rmsprop':
            opt = optimizers.RMSprop(learning_rate=learning_rate)
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer}")
        
        self.model.compile(
            optimizer=opt,
            loss=self._get_loss_function(),
            metrics=self._get_metrics()
        )
        
        logger.info(f"Model compiled with {optimizer} optimizer (lr={learning_rate})")
    
    def _get_loss_function(self):
        """Get loss function for the model. To be implemented by subclasses."""
        raise NotImplementedError
    
    def _get_metrics(self):
        """Get metrics for the model. To be implemented by subclasses."""
        raise NotImplementedError
    
    def get_callbacks(self, patience: int = 10, 
                     reduce_lr_patience: int = 5) -> List[callbacks.Callback]:
        """
        Get standard callbacks for training.
        
        Args:
            patience: Patience for early stopping
            reduce_lr_patience: Patience for learning rate reduction
            
        Returns:
            List of Keras callbacks
        """
        callback_list = [
            callbacks.EarlyStopping(
                monitor='val_loss',
                patience=patience,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=reduce_lr_patience,
                min_lr=1e-7,
                verbose=1
            ),
            callbacks.ModelCheckpoint(
                filepath=f'{self.name}_best.h5',
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            )
        ]
        
        return callback_list
    
    def train(self, train_dataset, val_dataset, epochs: int = 100, 
              callbacks_list: Optional[List] = None) -> keras.callbacks.History:
        """
        Train the model.
        
        Args:
            train_dataset: Training dataset
            val_dataset: Validation dataset
            epochs: Number of epochs to train
            callbacks_list: Custom callbacks (if None, uses default)
            
        Returns:
            Training history
        """
        if self.model is None:
            raise ValueError("Model must be built and compiled before training")
        
        if callbacks_list is None:
            callbacks_list = self.get_callbacks()
        
        logger.info(f"Starting training for {epochs} epochs")
        
        self.history = self.model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=epochs,
            callbacks=callbacks_list,
            verbose=1
        )
        
        logger.info("Training completed")
        return self.history
    
    def save_model(self, filepath: str) -> None:
        """Save the trained model."""
        if self.model is None:
            raise ValueError("No model to save")
        
        self.model.save(filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load a trained model."""
        self.model = keras.models.load_model(filepath)
        logger.info(f"Model loaded from {filepath}")


class MultiDigitModel(BaseOCRModel):
    """Multi-digit CNN model for end-to-end digit sequence recognition."""
    
    def __init__(self, input_shape: Tuple[int, int, int] = (100, 400, 1), 
                 num_digits: int = 4, num_classes: int = 11):
        """
        Initialize multi-digit model.
        
        Args:
            input_shape: Shape of input images (height, width, channels)
            num_digits: Number of digits to predict
            num_classes: Number of classes (0-9 + unknown/empty)
        """
        super().__init__("multi_digit_model")
        self.input_shape = input_shape
        self.num_digits = num_digits
        self.num_classes = num_classes
        self.build_model()
    
    def build_model(self) -> None:
        """Build the multi-digit CNN architecture."""
        # Input layer
        inputs = layers.Input(shape=self.input_shape, name='input_image')
        
        # Convolutional backbone
        x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.Dropout(0.25)(x)
        
        x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.Dropout(0.25)(x)
        
        x = layers.Conv2D(128, (3, 3), activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.Dropout(0.25)(x)
        
        x = layers.Conv2D(256, (3, 3), activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.Dropout(0.25)(x)
        
        # Global average pooling instead of flatten for better generalization
        x = layers.GlobalAveragePooling2D()(x)
        
        # Dense layers
        x = layers.Dense(512, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        x = layers.Dense(256, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        # Output layers for each digit position
        outputs = []
        for i in range(self.num_digits):
            digit_output = layers.Dense(
                self.num_classes, 
                activation='softmax', 
                name=f'digit_{i+1}'
            )(x)
            outputs.append(digit_output)
        
        # Create model
        self.model = models.Model(inputs=inputs, outputs=outputs, name=self.name)
        
        logger.info(f"Multi-digit model built with input shape {self.input_shape}")
        logger.info(f"Model has {self.model.count_params():,} parameters")
    
    def _get_loss_function(self):
        """Get loss function for multi-digit classification."""
        return {f'digit_{i+1}': 'sparse_categorical_crossentropy' for i in range(self.num_digits)}
    
    def _get_metrics(self):
        """Get metrics for multi-digit classification."""
        return {f'digit_{i+1}': ['accuracy'] for i in range(self.num_digits)}
    
    def predict_sequence(self, images: np.ndarray) -> np.ndarray:
        """
        Predict digit sequences from images.
        
        Args:
            images: Array of preprocessed images
            
        Returns:
            Array of predicted digit sequences
        """
        if self.model is None:
            raise ValueError("Model must be built before prediction")
        
        predictions = self.model.predict(images)
        
        # Convert predictions to digit sequences
        sequences = []
        for i in range(len(images)):
            sequence = []
            for j in range(self.num_digits):
                digit_pred = np.argmax(predictions[j][i])
                sequence.append(digit_pred)
            sequences.append(sequence)
        
        return np.array(sequences)


class SingleDigitModel(BaseOCRModel):
    """Single digit CNN model for individual digit classification."""
    
    def __init__(self, input_shape: Tuple[int, int, int] = (64, 32, 1), 
                 num_classes: int = 11):
        """
        Initialize single digit model.
        
        Args:
            input_shape: Shape of input images (height, width, channels)
            num_classes: Number of classes (0-9 + unknown/empty)
        """
        super().__init__("single_digit_model")
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.build_model()
    
    def build_model(self) -> None:
        """Build the single digit CNN architecture."""
        # Input layer
        inputs = layers.Input(shape=self.input_shape, name='input_digit')
        
        # Convolutional layers
        x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.Dropout(0.25)(x)
        
        x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.Dropout(0.25)(x)
        
        x = layers.Conv2D(128, (3, 3), activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.Dropout(0.25)(x)
        
        # Global average pooling
        x = layers.GlobalAveragePooling2D()(x)
        
        # Dense layers
        x = layers.Dense(128, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        x = layers.Dense(64, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        # Output layer
        outputs = layers.Dense(self.num_classes, activation='softmax', name='digit_output')(x)
        
        # Create model
        self.model = models.Model(inputs=inputs, outputs=outputs, name=self.name)
        
        logger.info(f"Single digit model built with input shape {self.input_shape}")
        logger.info(f"Model has {self.model.count_params():,} parameters")
    
    def _get_loss_function(self):
        """Get loss function for single digit classification."""
        return 'sparse_categorical_crossentropy'
    
    def _get_metrics(self):
        """Get metrics for single digit classification."""
        return ['accuracy', 'top_2_accuracy']
    
    def predict_digits(self, images: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Predict digits from images.
        
        Args:
            images: Array of preprocessed digit images
            
        Returns:
            Tuple of (predicted_classes, prediction_probabilities)
        """
        if self.model is None:
            raise ValueError("Model must be built before prediction")
        
        predictions = self.model.predict(images)
        predicted_classes = np.argmax(predictions, axis=1)
        
        return predicted_classes, predictions


# Convenience functions for model creation
def create_multi_digit_model(input_shape: Tuple[int, int, int] = (100, 400, 1)) -> MultiDigitModel:
    """Create a multi-digit model instance."""
    return MultiDigitModel(input_shape)


def create_single_digit_model(input_shape: Tuple[int, int, int] = (64, 32, 1)) -> SingleDigitModel:
    """Create a single digit model instance."""
    return SingleDigitModel(input_shape)


# Model utilities
def get_model_summary(model: BaseOCRModel) -> None:
    """Print model summary."""
    if model.model is None:
        print("Model not built yet")
        return
    
    print(f"\n{model.name.upper()} SUMMARY:")
    print("=" * 50)
    model.model.summary()
    
    
def plot_model_architecture(model: BaseOCRModel, filepath: str = None) -> None:
    """Plot model architecture."""
    if model.model is None:
        print("Model not built yet")
        return
    
    if filepath is None:
        filepath = f"{model.name}_architecture.png"
    
    keras.utils.plot_model(
        model.model,
        to_file=filepath,
        show_shapes=True,
        show_layer_names=True,
        rankdir='TB',
        expand_nested=True,
        dpi=96
    )
    
    print(f"Model architecture saved to {filepath}")


# GPU optimization utilities
def setup_gpu_memory_growth():
    """Setup GPU memory growth to avoid OOM errors."""
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            logger.info(f"GPU memory growth enabled for {len(gpus)} GPU(s)")
        except RuntimeError as e:
            logger.error(f"GPU setup error: {e}")
    else:
        logger.info("No GPUs found, using CPU")


def get_strategy():
    """Get distributed training strategy."""
    try:
        strategy = tf.distribute.MirroredStrategy()
        logger.info(f"Using MirroredStrategy with {strategy.num_replicas_in_sync} replicas")
        return strategy
    except:
        logger.info("Using default strategy (single device)")
        return tf.distribute.get_strategy()
