# Simple OCR Interface for Seven-Segment Displays

This is a simplified interface for the Seven-Segment-OCR project that provides an easy-to-use API: **provide image path + threshold → get OCR result**.

## 🚀 Quick Start

### Method 1: One-line OCR
```python
from simple_ocr import ocr_digit

# Simple usage
result = ocr_digit('digit_image.jpg', confidence_threshold=0.7)
print(f"Digit: {result['digit']}, Confidence: {result['confidence']:.3f}")
```

### Method 2: Using Your Google Colab Trained Model
```python
from simple_ocr import load_colab_model

# Load your trained model from Colab
ocr = load_colab_model('single_digit_ocr_model.keras')

# Use it
result = ocr.simple_ocr('digit_image.jpg', confidence_threshold=0.7)
print(result)
```

### Method 3: Class-based Usage
```python
from simple_ocr import SimpleOCR

# Initialize
ocr = SimpleOCR(model_path='your_model.keras', model_type='single')

# Use
result = ocr.simple_ocr('image.jpg', 0.7)
```

## 📋 API Reference

### Main Function: `simple_ocr(image_path, confidence_threshold)`

**Parameters:**
- `image_path` (str): Path to the digit image
- `confidence_threshold` (float): Minimum confidence to accept prediction (0.0 to 1.0)

**Returns:**
```python
{
    'digit': '3',           # Predicted digit (0-9) or 'unknown'
    'confidence': 0.856,    # Confidence score (0.0 to 1.0)
    'status': 'confident'   # 'confident', 'uncertain', or error message
}
```

### Status Values:
- `'confident'`: Prediction confidence >= threshold
- `'uncertain'`: Prediction confidence < threshold
- `'no_model_loaded'`: No model available
- `'preprocessing_failed'`: Image loading/processing failed
- `'prediction_error: ...'`: Error during prediction

## 🔧 Setup

### 1. Install Dependencies
```bash
pip install tensorflow opencv-python pillow numpy pandas scikit-learn
```

### 2. Use Your Google Colab Model
1. Download your trained model files from Colab:
   - `single_digit_ocr_model.keras` (recommended)
   - `single_digit_ocr_model.h5` (legacy)
   - `model_info.json` (metadata)

2. Place them in the same directory as `simple_ocr.py`

3. Load and use:
```python
from simple_ocr import load_colab_model
ocr = load_colab_model('single_digit_ocr_model.keras')
```

### 3. Or Train New Model with Existing Dataset
```python
from simple_ocr import SimpleOCR

ocr = SimpleOCR(model_type='single')
ocr.train_new_model(epochs=20)  # Uses existing dataset
ocr.save_model('my_model.keras')
```

## 📊 Examples

### Single Image
```python
result = ocr_digit('test/digit_3.jpg', 0.7)
if result['status'] == 'confident':
    print(f"Recognized: {result['digit']}")
else:
    print(f"Uncertain: {result['digit']} (confidence: {result['confidence']:.3f})")
```

### Batch Processing
```python
import os
from simple_ocr import load_colab_model

ocr = load_colab_model('single_digit_ocr_model.keras')

image_dir = 'test_images/'
for filename in os.listdir(image_dir):
    if filename.endswith('.jpg'):
        image_path = os.path.join(image_dir, filename)
        result = ocr.simple_ocr(image_path, 0.7)
        print(f"{filename}: {result['digit']} ({result['confidence']:.3f})")
```

### Different Confidence Thresholds
```python
image_path = 'uncertain_digit.jpg'
thresholds = [0.5, 0.7, 0.9]

for threshold in thresholds:
    result = ocr_digit(image_path, threshold)
    print(f"Threshold {threshold}: {result['digit']} - {result['status']}")
```

## 🎯 Model Compatibility

### Google Colab Model (Recommended)
- **Input**: (64, 64, 3) RGB images
- **Output**: 11 classes (digits 0-9 + unknown)
- **Format**: `.keras` or `.h5`
- **Preprocessing**: Automatic resize and normalization

### Legacy Dataset Model
- **Input**: (100, 256, 1) grayscale images
- **Output**: 11 classes (digits 0-9 + unknown)
- **Training**: Uses existing `Datasets_digits/` folder

## 🔍 Troubleshooting

### Common Issues:

1. **"No model loaded"**
   ```python
   # Solution: Load a model first
   ocr = SimpleOCR(model_path='your_model.keras')
   # Or train new one
   ocr.train_new_model()
   ```

2. **"Could not load image"**
   ```python
   # Check file path and format
   import os
   print(os.path.exists('your_image.jpg'))  # Should be True
   ```

3. **Low confidence predictions**
   ```python
   # Try lower threshold or check image quality
   result = ocr_digit('image.jpg', confidence_threshold=0.5)
   ```

4. **Import errors**
   ```bash
   # Install missing dependencies
   pip install tensorflow opencv-python pillow
   ```

## 📁 File Structure
```
Seven-Segment-OCR-master/
├── simple_ocr.py              # Main OCR interface
├── example_usage.py           # Usage examples
├── SIMPLE_OCR_README.md       # This file
├── Model.py                   # Original model classes
├── Datasets.py                # Original dataset classes
├── single_digit_ocr_model.keras  # Your Colab model (download)
├── test/                      # Test images
└── Datasets_digits/           # Training data (if available)
```

## 🎉 That's It!

Your OCR is now as simple as:
```python
result = ocr_digit('my_digit.jpg', 0.7)
print(result['digit'])  # → "3"
```

Perfect for production use! 🚀
