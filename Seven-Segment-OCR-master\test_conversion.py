"""
Test script to validate the Seven-Segment OCR modernization
Tests all major components to ensure they work correctly
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from pathlib import Path
import tempfile
import shutil

# Try to import TensorFlow
try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConversionTester:
    """
    Test suite for validating the modernized Seven-Segment OCR implementation.
    """
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.test_results = {}
        self.temp_dir = None
    
    def setup_test_environment(self):
        """Setup test environment using existing data."""
        try:
            # Use the actual project directory instead of temp
            self.temp_dir = self.project_root
            logger.info(f"Using project directory for testing: {self.temp_dir}")
            
            # Check if actual data directories exist
            test_dirs = ['Datasets', 'Datasets_frames', 'Datasets_digits']
            for dir_name in test_dirs:
                dir_path = os.path.join(self.temp_dir, dir_name)
                if not os.path.exists(dir_path):
                    logger.warning(f"Directory {dir_name} not found at {dir_path}")
                else:
                    logger.info(f"✅ Found {dir_name} directory")
            
            logger.info("✅ Test environment setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test environment setup failed: {e}")
            return False
    
    def test_imports(self):
        """Test that all modules can be imported successfully."""
        logger.info("🧪 Testing module imports...")

        # Updated module names to match your actual files
        modules_to_test = [
            'Datasets',          # Datasets.py (legacy)
            'Model',             # Model.py (legacy)
            'frame_extractor',   # frame_extractor.py
            'digits_cut',        # digits_cut.py
            'simple_ocr'         # simple_ocr.py (our new file)
        ]
        
        import_results = {}
        
        for module_name in modules_to_test:
            try:
                # Add current directory to path for imports
                if str(self.project_root) not in sys.path:
                    sys.path.insert(0, str(self.project_root))
                
                __import__(module_name)
                import_results[module_name] = True
                logger.info(f"✅ {module_name} imported successfully")
                
            except Exception as e:
                import_results[module_name] = False
                logger.error(f"❌ {module_name} import failed: {e}")
        
        self.test_results['imports'] = import_results
        return all(import_results.values())
    
    def test_dataset_classes(self):
        """Test dataset classes functionality."""
        logger.info("🧪 Testing dataset classes...")

        try:
            from Datasets import Dataset_Multi, Dataset_Single

            # Test Dataset_Multi with actual data paths
            dataset = Dataset_Multi(
                data_dir=os.path.join(self.temp_dir, 'Datasets'),
                frame_dir=os.path.join(self.temp_dir, 'Datasets_frames')
            )

            # Test basic dataset functionality
            logger.info(f"✅ Dataset_Multi created successfully")

            # Test Dataset_Single
            single_dataset = Dataset_Single(
                data_dir=os.path.join(self.temp_dir, 'Datasets_Eleven')
            )

            logger.info(f"✅ Dataset_Single created successfully")

            logger.info("✅ Dataset classes working correctly")
            self.test_results['datasets'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Dataset classes test failed: {e}")
            self.test_results['datasets'] = False
            return False
    
    def test_model_classes(self):
        """Test model classes functionality."""
        logger.info("🧪 Testing model classes...")

        try:
            from Model import Model_Multi, Model_Single

            # Test Model_Multi
            multi_model = Model_Multi()
            logger.info("✅ Model_Multi created successfully")

            # Test Model_Single
            single_model = Model_Single()
            logger.info("✅ Model_Single created successfully")

            logger.info("✅ Model classes working correctly")
            self.test_results['models'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Model classes test failed: {e}")
            self.test_results['models'] = False
            return False
    
    def test_simple_ocr(self):
        """Test our simple OCR interface."""
        logger.info("🧪 Testing Simple OCR interface...")
        
        try:
            from simple_ocr import SimpleOCR, ocr_digit
            
            # Test SimpleOCR class creation
            ocr = SimpleOCR(model_type='single')
            assert ocr.model_type == 'single', "Model type should be set correctly"
            logger.info("✅ SimpleOCR class created successfully")
            
            # Test with actual test images if available
            test_dir = os.path.join(self.temp_dir, 'test')
            if os.path.exists(test_dir):
                test_images = [f for f in os.listdir(test_dir) if f.endswith('.jpg')]
                if test_images:
                    test_image = os.path.join(test_dir, test_images[0])
                    logger.info(f"✅ Found test image: {test_image}")
                    
                    # Test preprocessing
                    img = ocr.preprocess_image(test_image)
                    assert img is not None, "Image preprocessing should work"
                    logger.info(f"✅ Image preprocessing works, shape: {img.shape}")
            
            logger.info("✅ Simple OCR interface working correctly")
            self.test_results['simple_ocr'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Simple OCR test failed: {e}")
            self.test_results['simple_ocr'] = False
            return False
    
    def test_frame_extractor(self):
        """Test frame extractor functionality."""
        logger.info("🧪 Testing frame extractor...")

        try:
            import frame_extractor
            logger.info("✅ Frame extractor module imported successfully")

            self.test_results['frame_extractor'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Frame extractor test failed: {e}")
            self.test_results['frame_extractor'] = False
            return False

    def test_tensorflow_compatibility(self):
        """Test TensorFlow 2.x compatibility."""
        logger.info("🧪 Testing TensorFlow compatibility...")

        try:
            import tensorflow as tf

            # Check TensorFlow version
            tf_version = tf.__version__
            major_version = int(tf_version.split('.')[0])
            assert major_version >= 2, f"TensorFlow 2.x required, found {tf_version}"

            # Test basic TensorFlow operations
            test_tensor = tf.constant([[1, 2], [3, 4]], dtype=tf.float32)
            result = tf.reduce_sum(test_tensor)
            assert result.numpy() == 10.0, "Basic TensorFlow operations should work"

            # Test Keras availability
            from tensorflow import keras
            assert hasattr(keras, 'layers'), "Keras layers should be available"
            assert hasattr(keras, 'Model'), "Keras Model should be available"

            logger.info(f"✅ TensorFlow {tf_version} compatibility confirmed")
            self.test_results['tensorflow'] = True
            return True

        except Exception as e:
            logger.error(f"❌ TensorFlow compatibility test failed: {e}")
            self.test_results['tensorflow'] = False
            return False
    
    def cleanup_test_environment(self):
        """Clean up test environment (no-op since we use actual directory)."""
        logger.info("🧹 Test environment cleanup complete (using actual directory)")
    
    def run_all_tests(self):
        """Run all validation tests."""
        logger.info("🚀 Starting Seven-Segment OCR conversion validation...")
        
        # Setup test environment
        if not self.setup_test_environment():
            logger.error("❌ Failed to setup test environment")
            return False
        
        try:
            # Define test functions
            tests = [
                ("TensorFlow Compatibility", self.test_tensorflow_compatibility),
                ("Module Imports", self.test_imports),
                ("Dataset Classes", self.test_dataset_classes),
                ("Model Classes", self.test_model_classes),
                ("Simple OCR Interface", self.test_simple_ocr),
                ("Frame Extractor", self.test_frame_extractor)
            ]
            
            # Run tests
            passed_tests = 0
            total_tests = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"\n📋 Running {test_name} test...")
                try:
                    if test_func():
                        passed_tests += 1
                        logger.info(f"✅ {test_name} test PASSED")
                    else:
                        logger.error(f"❌ {test_name} test FAILED")
                except Exception as e:
                    logger.error(f"❌ {test_name} test FAILED with exception: {e}")
            
            # Print summary
            logger.info(f"\n📊 Test Summary:")
            logger.info(f"   Passed: {passed_tests}/{total_tests}")
            logger.info(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
            
            if passed_tests == total_tests:
                logger.info("🎉 All tests PASSED! Conversion is successful.")
                return True
            else:
                logger.warning("⚠️ Some tests FAILED. Please review the issues above.")
                return False
                
        finally:
            self.cleanup_test_environment()


def main():
    """Main test function."""
    # Add TensorFlow import check
    try:
        import tensorflow as tf
        logger.info(f"TensorFlow {tf.__version__} detected")
    except ImportError:
        logger.error("❌ TensorFlow not available. Please install requirements first.")
        return False
    
    # Run tests
    tester = ConversionTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Seven-Segment OCR modernization validation SUCCESSFUL!")
        print("The converted codebase is ready for use.")
        print("\nNext steps:")
        print("1. Use simple_ocr.py for easy OCR interface")
        print("2. Train models using the modernized classes")
        print("3. Test with your actual images!")
    else:
        print("\n⚠️ Validation completed with issues.")
        print("Please review the test results and fix any failing components.")
    
    return success


if __name__ == "__main__":
    main()
