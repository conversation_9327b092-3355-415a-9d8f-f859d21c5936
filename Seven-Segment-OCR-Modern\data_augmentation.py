"""
Modern Data Augmentation and Optimization for Seven-Segment OCR
Implements advanced augmentation techniques for better model generalization
"""

import tensorflow as tf
import numpy as np
import cv2
from typing import Tuple, List, Optional, Callable
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AdvancedDataAugmentation:
    """
    Advanced data augmentation pipeline for seven-segment display images.
    """
    
    def __init__(self, 
                 rotation_range: float = 5.0,
                 brightness_range: Tuple[float, float] = (0.8, 1.2),
                 contrast_range: Tuple[float, float] = (0.8, 1.2),
                 noise_factor: float = 0.05,
                 blur_probability: float = 0.3,
                 elastic_probability: float = 0.2):
        """
        Initialize augmentation pipeline.
        
        Args:
            rotation_range: Maximum rotation angle in degrees
            brightness_range: Range for brightness adjustment
            contrast_range: Range for contrast adjustment
            noise_factor: Maximum noise factor to add
            blur_probability: Probability of applying blur
            elastic_probability: Probability of applying elastic deformation
        """
        self.rotation_range = rotation_range
        self.brightness_range = brightness_range
        self.contrast_range = contrast_range
        self.noise_factor = noise_factor
        self.blur_probability = blur_probability
        self.elastic_probability = elastic_probability
    
    def random_rotation(self, image: tf.Tensor) -> tf.Tensor:
        """Apply random rotation within specified range."""
        angle = tf.random.uniform([], -self.rotation_range, self.rotation_range)
        angle_rad = angle * np.pi / 180.0
        return tf.image.rot90(image, k=tf.cast(angle_rad / (np.pi/2), tf.int32))
    
    def random_brightness(self, image: tf.Tensor) -> tf.Tensor:
        """Apply random brightness adjustment."""
        factor = tf.random.uniform([], self.brightness_range[0], self.brightness_range[1])
        return tf.image.adjust_brightness(image, factor - 1.0)
    
    def random_contrast(self, image: tf.Tensor) -> tf.Tensor:
        """Apply random contrast adjustment."""
        factor = tf.random.uniform([], self.contrast_range[0], self.contrast_range[1])
        return tf.image.adjust_contrast(image, factor)
    
    def add_noise(self, image: tf.Tensor) -> tf.Tensor:
        """Add random Gaussian noise."""
        noise = tf.random.normal(tf.shape(image), mean=0.0, stddev=self.noise_factor)
        noisy_image = image + noise
        return tf.clip_by_value(noisy_image, 0.0, 1.0)
    
    def random_blur(self, image: tf.Tensor) -> tf.Tensor:
        """Apply random Gaussian blur."""
        def apply_blur():
            # Convert to uint8 for OpenCV operations
            image_uint8 = tf.cast(image * 255, tf.uint8)
            # Apply Gaussian blur
            blurred = tf.py_function(
                func=lambda x: cv2.GaussianBlur(x.numpy(), (3, 3), 0),
                inp=[image_uint8],
                Tout=tf.uint8
            )
            return tf.cast(blurred, tf.float32) / 255.0
        
        def no_blur():
            return image
        
        # Apply blur with specified probability
        return tf.cond(
            tf.random.uniform([]) < self.blur_probability,
            apply_blur,
            no_blur
        )
    
    def elastic_deformation(self, image: tf.Tensor) -> tf.Tensor:
        """Apply elastic deformation for more realistic variations."""
        def apply_elastic():
            # Simple elastic-like transformation using random displacement
            height, width = tf.shape(image)[0], tf.shape(image)[1]
            
            # Create displacement fields
            dx = tf.random.normal([height, width], mean=0.0, stddev=2.0)
            dy = tf.random.normal([height, width], mean=0.0, stddev=2.0)
            
            # Create coordinate grids
            x, y = tf.meshgrid(tf.range(width, dtype=tf.float32), 
                              tf.range(height, dtype=tf.float32))
            
            # Apply displacement
            x_displaced = x + dx
            y_displaced = y + dy
            
            # Normalize coordinates to [-1, 1] for grid_sample
            x_norm = (x_displaced / tf.cast(width - 1, tf.float32)) * 2.0 - 1.0
            y_norm = (y_displaced / tf.cast(height - 1, tf.float32)) * 2.0 - 1.0
            
            # Stack coordinates
            grid = tf.stack([x_norm, y_norm], axis=-1)
            grid = tf.expand_dims(grid, 0)  # Add batch dimension
            
            # Apply transformation
            image_batch = tf.expand_dims(image, 0)
            transformed = tf.nn.grid_sample(image_batch, grid, align_corners=True)
            
            return tf.squeeze(transformed, 0)
        
        def no_elastic():
            return image
        
        return tf.cond(
            tf.random.uniform([]) < self.elastic_probability,
            apply_elastic,
            no_elastic
        )
    
    def augment_image(self, image: tf.Tensor) -> tf.Tensor:
        """Apply full augmentation pipeline to a single image."""
        # Ensure image is in float32 format
        image = tf.cast(image, tf.float32)
        
        # Apply augmentations in sequence
        image = self.random_brightness(image)
        image = self.random_contrast(image)
        image = self.add_noise(image)
        image = self.random_blur(image)
        image = self.elastic_deformation(image)
        
        # Ensure values are in valid range
        image = tf.clip_by_value(image, 0.0, 1.0)
        
        return image
    
    def create_augmentation_fn(self) -> Callable:
        """Create a function for use with tf.data.Dataset.map()."""
        def augment_fn(image, label):
            augmented_image = self.augment_image(image)
            return augmented_image, label
        
        return augment_fn


class OptimizedDataPipeline:
    """
    Optimized data pipeline for efficient training.
    """
    
    def __init__(self, 
                 batch_size: int = 32,
                 prefetch_buffer: int = tf.data.AUTOTUNE,
                 num_parallel_calls: int = tf.data.AUTOTUNE):
        """
        Initialize optimized data pipeline.
        
        Args:
            batch_size: Batch size for training
            prefetch_buffer: Prefetch buffer size
            num_parallel_calls: Number of parallel calls for map operations
        """
        self.batch_size = batch_size
        self.prefetch_buffer = prefetch_buffer
        self.num_parallel_calls = num_parallel_calls
    
    def create_optimized_dataset(self, 
                                images: np.ndarray,
                                labels: np.ndarray,
                                augmentation: Optional[AdvancedDataAugmentation] = None,
                                shuffle: bool = True,
                                repeat: bool = True) -> tf.data.Dataset:
        """
        Create an optimized TensorFlow dataset.
        
        Args:
            images: Array of images
            labels: Array of labels
            augmentation: Data augmentation pipeline
            shuffle: Whether to shuffle the dataset
            repeat: Whether to repeat the dataset
            
        Returns:
            Optimized TensorFlow dataset
        """
        # Create dataset from arrays
        dataset = tf.data.Dataset.from_tensor_slices((images, labels))
        
        # Shuffle if requested
        if shuffle:
            dataset = dataset.shuffle(buffer_size=len(images))
        
        # Apply augmentation if provided
        if augmentation is not None:
            augment_fn = augmentation.create_augmentation_fn()
            dataset = dataset.map(augment_fn, num_parallel_calls=self.num_parallel_calls)
        
        # Batch the dataset
        dataset = dataset.batch(self.batch_size)
        
        # Repeat if requested
        if repeat:
            dataset = dataset.repeat()
        
        # Prefetch for performance
        dataset = dataset.prefetch(self.prefetch_buffer)
        
        return dataset
    
    def create_mixed_precision_strategy(self) -> tf.distribute.Strategy:
        """Create mixed precision strategy for faster training."""
        # Enable mixed precision
        policy = tf.keras.mixed_precision.Policy('mixed_float16')
        tf.keras.mixed_precision.set_global_policy(policy)
        
        # Get distribution strategy
        strategy = tf.distribute.get_strategy()
        
        logger.info(f"Mixed precision enabled with policy: {policy.name}")
        logger.info(f"Using strategy: {strategy}")
        
        return strategy


class PerformanceOptimizer:
    """
    Performance optimization utilities for training.
    """
    
    @staticmethod
    def setup_gpu_optimization():
        """Setup GPU for optimal performance."""
        gpus = tf.config.experimental.list_physical_devices('GPU')
        
        if gpus:
            try:
                # Enable memory growth
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                
                # Set memory limit if needed (uncomment and adjust as needed)
                # tf.config.experimental.set_memory_limit(gpus[0], 4096)
                
                logger.info(f"GPU optimization enabled for {len(gpus)} GPU(s)")
                
            except RuntimeError as e:
                logger.error(f"GPU setup error: {e}")
        else:
            logger.info("No GPUs found, using CPU")
    
    @staticmethod
    def create_learning_rate_schedule(initial_lr: float = 0.001,
                                    decay_steps: int = 1000,
                                    decay_rate: float = 0.9) -> tf.keras.optimizers.schedules.LearningRateSchedule:
        """Create exponential decay learning rate schedule."""
        schedule = tf.keras.optimizers.schedules.ExponentialDecay(
            initial_learning_rate=initial_lr,
            decay_steps=decay_steps,
            decay_rate=decay_rate,
            staircase=True
        )
        
        logger.info(f"Learning rate schedule created: initial_lr={initial_lr}, "
                   f"decay_steps={decay_steps}, decay_rate={decay_rate}")
        
        return schedule
    
    @staticmethod
    def create_advanced_callbacks(model_name: str = "model",
                                patience: int = 10,
                                reduce_lr_patience: int = 5) -> List[tf.keras.callbacks.Callback]:
        """Create advanced callbacks for training."""
        callbacks = [
            # Early stopping
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=patience,
                restore_best_weights=True,
                verbose=1
            ),
            
            # Learning rate reduction
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=reduce_lr_patience,
                min_lr=1e-7,
                verbose=1
            ),
            
            # Model checkpointing
            tf.keras.callbacks.ModelCheckpoint(
                filepath=f'{model_name}_best.h5',
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),
            
            # TensorBoard logging
            tf.keras.callbacks.TensorBoard(
                log_dir=f'./logs/{model_name}',
                histogram_freq=1,
                write_graph=True,
                write_images=True
            ),
            
            # CSV logging
            tf.keras.callbacks.CSVLogger(
                filename=f'{model_name}_training.csv',
                separator=',',
                append=False
            )
        ]
        
        logger.info(f"Created {len(callbacks)} advanced callbacks")
        return callbacks


# Convenience functions
def create_augmented_dataset(images: np.ndarray, 
                           labels: np.ndarray,
                           batch_size: int = 32,
                           augmentation_strength: str = 'medium') -> tf.data.Dataset:
    """
    Create an augmented dataset with predefined augmentation levels.
    
    Args:
        images: Training images
        labels: Training labels
        batch_size: Batch size
        augmentation_strength: 'light', 'medium', or 'heavy'
        
    Returns:
        Augmented TensorFlow dataset
    """
    # Define augmentation parameters based on strength
    if augmentation_strength == 'light':
        augmentation = AdvancedDataAugmentation(
            rotation_range=2.0,
            brightness_range=(0.9, 1.1),
            contrast_range=(0.9, 1.1),
            noise_factor=0.02,
            blur_probability=0.1,
            elastic_probability=0.1
        )
    elif augmentation_strength == 'medium':
        augmentation = AdvancedDataAugmentation(
            rotation_range=5.0,
            brightness_range=(0.8, 1.2),
            contrast_range=(0.8, 1.2),
            noise_factor=0.05,
            blur_probability=0.3,
            elastic_probability=0.2
        )
    elif augmentation_strength == 'heavy':
        augmentation = AdvancedDataAugmentation(
            rotation_range=10.0,
            brightness_range=(0.7, 1.3),
            contrast_range=(0.7, 1.3),
            noise_factor=0.1,
            blur_probability=0.5,
            elastic_probability=0.4
        )
    else:
        raise ValueError("augmentation_strength must be 'light', 'medium', or 'heavy'")
    
    # Create optimized pipeline
    pipeline = OptimizedDataPipeline(batch_size=batch_size)
    dataset = pipeline.create_optimized_dataset(images, labels, augmentation)
    
    logger.info(f"Created augmented dataset with {augmentation_strength} augmentation")
    return dataset


def setup_training_environment(model_name: str = "seven_segment_ocr") -> dict:
    """
    Setup complete training environment with optimizations.
    
    Args:
        model_name: Name for the model and logs
        
    Returns:
        Dictionary with training components
    """
    # Setup GPU optimization
    PerformanceOptimizer.setup_gpu_optimization()
    
    # Create mixed precision strategy
    pipeline = OptimizedDataPipeline()
    strategy = pipeline.create_mixed_precision_strategy()
    
    # Create learning rate schedule
    lr_schedule = PerformanceOptimizer.create_learning_rate_schedule()
    
    # Create callbacks
    callbacks = PerformanceOptimizer.create_advanced_callbacks(model_name)
    
    return {
        'strategy': strategy,
        'lr_schedule': lr_schedule,
        'callbacks': callbacks,
        'pipeline': pipeline
    }
