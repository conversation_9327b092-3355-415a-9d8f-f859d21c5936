"""
Test script to validate the Seven-Segment OCR modernization
Tests all major components to ensure they work correctly
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from pathlib import Path
import tempfile
import shutil

# Try to import TensorFlow
try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConversionTester:
    """
    Test suite for validating the modernized Seven-Segment OCR implementation.
    """
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.test_results = {}
        self.temp_dir = None
    
    def setup_test_environment(self):
        """Setup temporary test environment."""
        try:
            self.temp_dir = tempfile.mkdtemp(prefix='seven_segment_test_')
            logger.info(f"Created temporary test directory: {self.temp_dir}")
            
            # Create test data directories
            test_dirs = ['Datasets', 'Datasets_frames', 'Datasets_digits']
            for dir_name in test_dirs:
                os.makedirs(os.path.join(self.temp_dir, dir_name), exist_ok=True)
            
            # Create test CSV data
            test_data = {
                'used_liter': [33, 29, 230],
                'image': ['test1.jpg', 'test2.jpg', 'test3.jpg'],
                'cadran_1': [0, 1, 2],
                'cadran_2': [3, 2, 3],
                'cadran_3': [3, 9, 0],
                'cadran_4': [3, 9, 0]
            }
            
            df = pd.DataFrame(test_data)
            csv_path = os.path.join(self.temp_dir, 'Datasets', 'test_data.csv')
            df.to_csv(csv_path, sep=';', index_label='index')
            
            # Create dummy test images
            import cv2
            for img_name in test_data['image']:
                # Create a simple test image (100x400 grayscale)
                test_img = np.random.randint(0, 255, (100, 400), dtype=np.uint8)
                img_path = os.path.join(self.temp_dir, 'Datasets_frames', img_name)
                cv2.imwrite(img_path, test_img)
            
            logger.info("✅ Test environment setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test environment setup failed: {e}")
            return False
    
    def test_imports(self):
        """Test that all modules can be imported successfully."""
        logger.info("🧪 Testing module imports...")
        
        modules_to_test = [
            'datasets',
            'models', 
            'frame_extractor',
            'digit_cutter',
            'data_augmentation',
            'setup'
        ]
        
        import_results = {}
        
        for module_name in modules_to_test:
            try:
                # Add current directory to path for imports
                if str(self.project_root) not in sys.path:
                    sys.path.insert(0, str(self.project_root))
                
                __import__(module_name)
                import_results[module_name] = True
                logger.info(f"✅ {module_name} imported successfully")
                
            except Exception as e:
                import_results[module_name] = False
                logger.error(f"❌ {module_name} import failed: {e}")
        
        self.test_results['imports'] = import_results
        return all(import_results.values())
    
    def test_dataset_classes(self):
        """Test dataset classes functionality."""
        logger.info("🧪 Testing dataset classes...")
        
        try:
            from datasets import MultiDigitDataset, SingleDigitDataset, BaseDataset
            
            # Test MultiDigitDataset
            dataset = MultiDigitDataset(
                data_dir=os.path.join(self.temp_dir, 'Datasets'),
                frame_dir=os.path.join(self.temp_dir, 'Datasets_frames')
            )
            
            # Test data loading
            data = dataset.data
            assert len(data) > 0, "Dataset should contain data"
            
            # Test image loading
            images = dataset.load_images(data['image'].values[:2])
            assert images.shape[0] == 2, "Should load 2 images"
            assert len(images.shape) == 4, "Images should be 4D (batch, height, width, channels)"
            
            logger.info("✅ Dataset classes working correctly")
            self.test_results['datasets'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Dataset classes test failed: {e}")
            self.test_results['datasets'] = False
            return False
    
    def test_model_classes(self):
        """Test model classes functionality."""
        logger.info("🧪 Testing model classes...")
        
        try:
            from models import MultiDigitModel, SingleDigitModel
            
            # Test MultiDigitModel
            multi_model = MultiDigitModel()
            model = multi_model.build_model()
            
            assert model is not None, "Model should be created"
            assert len(model.outputs) == 4, "Multi-digit model should have 4 outputs"
            
            # Test model compilation
            multi_model.compile_model()
            
            # Test SingleDigitModel
            single_model = SingleDigitModel()
            single_model_obj = single_model.build_model()
            
            assert single_model_obj is not None, "Single digit model should be created"
            
            logger.info("✅ Model classes working correctly")
            self.test_results['models'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Model classes test failed: {e}")
            self.test_results['models'] = False
            return False
    
    def test_frame_extractor(self):
        """Test frame extractor functionality."""
        logger.info("🧪 Testing frame extractor...")
        
        try:
            from frame_extractor import ModernFrameExtractor
            
            # Create test image
            test_img = np.random.randint(0, 255, (200, 800, 3), dtype=np.uint8)
            
            # Test frame extractor
            extractor = ModernFrameExtractor()
            
            # Test frame detection
            frame = extractor.detect_frame(test_img)
            assert frame is not None, "Frame detection should return a result"
            
            # Test preprocessing
            preprocessed = extractor.preprocess_frame(test_img)
            assert preprocessed is not None, "Frame preprocessing should work"
            
            logger.info("✅ Frame extractor working correctly")
            self.test_results['frame_extractor'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Frame extractor test failed: {e}")
            self.test_results['frame_extractor'] = False
            return False
    
    def test_digit_cutter(self):
        """Test digit cutter functionality."""
        logger.info("🧪 Testing digit cutter...")
        
        try:
            from digit_cutter import ModernDigitCutter
            
            # Create test image
            test_img = np.random.randint(0, 255, (100, 400), dtype=np.uint8)
            
            # Test digit cutter
            cutter = ModernDigitCutter(
                image=test_img,
                labels=[1, 2, 3, 4]
            )
            
            # Test simple extraction
            success = cutter.extract_digits_simple()
            assert success, "Simple digit extraction should succeed"
            assert len(cutter.digit_boxes) == 4, "Should extract 4 digits"
            
            # Test adaptive extraction
            success = cutter.extract_digits_adaptive()
            assert success, "Adaptive digit extraction should succeed"
            
            logger.info("✅ Digit cutter working correctly")
            self.test_results['digit_cutter'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Digit cutter test failed: {e}")
            self.test_results['digit_cutter'] = False
            return False
    
    def test_data_augmentation(self):
        """Test data augmentation functionality."""
        logger.info("🧪 Testing data augmentation...")
        
        try:
            from data_augmentation import AdvancedDataAugmentation, create_augmented_dataset
            
            # Test augmentation class
            augmentation = AdvancedDataAugmentation()
            
            # Create test image
            test_img = np.random.rand(100, 400, 1).astype(np.float32)
            test_tensor = tf.constant(test_img)
            
            # Test individual augmentation methods
            augmented = augmentation.augment_image(test_tensor)
            assert augmented.shape == test_tensor.shape, "Augmented image should maintain shape"
            
            # Test augmentation function creation
            augment_fn = augmentation.create_augmentation_fn()
            assert callable(augment_fn), "Should create callable augmentation function"
            
            logger.info("✅ Data augmentation working correctly")
            self.test_results['data_augmentation'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Data augmentation test failed: {e}")
            self.test_results['data_augmentation'] = False
            return False
    
    def test_setup_script(self):
        """Test setup script functionality."""
        logger.info("🧪 Testing setup script...")
        
        try:
            from setup import SevenSegmentOCRSetup
            
            # Test setup class creation
            setup = SevenSegmentOCRSetup(environment='local')
            assert setup.environment == 'local', "Environment should be set correctly"
            
            # Test directory creation (in temp directory)
            original_cwd = os.getcwd()
            os.chdir(self.temp_dir)
            
            try:
                success = setup.setup_directories()
                assert success, "Directory setup should succeed"
                
                # Check that directories were created
                required_dirs = ['models', 'logs', 'outputs']
                for dir_name in required_dirs:
                    assert os.path.exists(dir_name), f"Directory {dir_name} should exist"
                
            finally:
                os.chdir(original_cwd)
            
            logger.info("✅ Setup script working correctly")
            self.test_results['setup'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup script test failed: {e}")
            self.test_results['setup'] = False
            return False
    
    def test_tensorflow_compatibility(self):
        """Test TensorFlow 2.x compatibility."""
        logger.info("🧪 Testing TensorFlow compatibility...")
        
        try:
            import tensorflow as tf
            
            # Check TensorFlow version
            tf_version = tf.__version__
            major_version = int(tf_version.split('.')[0])
            assert major_version >= 2, f"TensorFlow 2.x required, found {tf_version}"
            
            # Test basic TensorFlow operations
            test_tensor = tf.constant([[1, 2], [3, 4]], dtype=tf.float32)
            result = tf.reduce_sum(test_tensor)
            assert result.numpy() == 10.0, "Basic TensorFlow operations should work"
            
            # Test Keras availability
            from tensorflow import keras
            assert hasattr(keras, 'layers'), "Keras layers should be available"
            assert hasattr(keras, 'Model'), "Keras Model should be available"
            
            logger.info(f"✅ TensorFlow {tf_version} compatibility confirmed")
            self.test_results['tensorflow'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ TensorFlow compatibility test failed: {e}")
            self.test_results['tensorflow'] = False
            return False
    
    def cleanup_test_environment(self):
        """Clean up temporary test environment."""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                logger.info("🧹 Test environment cleaned up")
            except Exception as e:
                logger.warning(f"⚠️ Failed to clean up test environment: {e}")
    
    def run_all_tests(self):
        """Run all validation tests."""
        logger.info("🚀 Starting Seven-Segment OCR conversion validation...")
        
        # Setup test environment
        if not self.setup_test_environment():
            logger.error("❌ Failed to setup test environment")
            return False
        
        try:
            # Define test functions
            tests = [
                ("TensorFlow Compatibility", self.test_tensorflow_compatibility),
                ("Module Imports", self.test_imports),
                ("Dataset Classes", self.test_dataset_classes),
                ("Model Classes", self.test_model_classes),
                ("Frame Extractor", self.test_frame_extractor),
                ("Digit Cutter", self.test_digit_cutter),
                ("Data Augmentation", self.test_data_augmentation),
                ("Setup Script", self.test_setup_script)
            ]
            
            # Run tests
            passed_tests = 0
            total_tests = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"\n📋 Running {test_name} test...")
                try:
                    if test_func():
                        passed_tests += 1
                        logger.info(f"✅ {test_name} test PASSED")
                    else:
                        logger.error(f"❌ {test_name} test FAILED")
                except Exception as e:
                    logger.error(f"❌ {test_name} test FAILED with exception: {e}")
            
            # Print summary
            logger.info(f"\n📊 Test Summary:")
            logger.info(f"   Passed: {passed_tests}/{total_tests}")
            logger.info(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
            
            if passed_tests == total_tests:
                logger.info("🎉 All tests PASSED! Conversion is successful.")
                return True
            else:
                logger.warning("⚠️ Some tests FAILED. Please review the issues above.")
                return False
                
        finally:
            self.cleanup_test_environment()


def main():
    """Main test function."""
    # Add TensorFlow import check
    try:
        import tensorflow as tf
        logger.info(f"TensorFlow {tf.__version__} detected")
    except ImportError:
        logger.error("❌ TensorFlow not available. Please install requirements first.")
        return False
    
    # Run tests
    tester = ConversionTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Seven-Segment OCR modernization validation SUCCESSFUL!")
        print("The converted codebase is ready for use in Google Colab.")
        print("\nNext steps:")
        print("1. Upload the Seven-Segment-OCR-Modern folder to Google Colab")
        print("2. Open Seven_Segment_OCR_Modern.ipynb")
        print("3. Upload your dataset and start training!")
    else:
        print("\n⚠️ Validation completed with issues.")
        print("Please review the test results and fix any failing components.")
    
    return success


if __name__ == "__main__":
    main()
