"""
Modern Dataset Classes for Seven-Segment OCR
Compatible with TensorFlow 2.x and modern Python environments
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import tensorflow as tf
from sklearn.model_selection import train_test_split
from typing import Tuple, List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BaseDataset:
    """Base class for dataset handling with modern TensorFlow integration."""
    
    def __init__(self, data_dir: str = 'Datasets'):
        """
        Initialize base dataset.
        
        Args:
            data_dir: Directory containing CSV files with labels
        """
        self.data_dir = data_dir
        self.data = self._load_full_data()
        logger.info(f"Loaded dataset with {len(self.data)} samples")
    
    def _load_full_data(self) -> pd.DataFrame:
        """Load and combine all CSV files in the data directory."""
        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
        
        if not csv_files:
            raise FileNotFoundError(f"No CSV files found in {self.data_dir}")
        
        dataframes = []
        for csv_file in csv_files:
            file_path = os.path.join(self.data_dir, csv_file)
            df = pd.read_csv(file_path, sep=';', index_col=0)
            dataframes.append(df)
            logger.info(f"Loaded {len(df)} samples from {csv_file}")
        
        combined_data = pd.concat(dataframes, axis=0, ignore_index=True)
        # Replace 'X' with 10 for empty/unknown digits
        combined_data = combined_data.replace("X", 10)
        
        return combined_data
    
    def get_data_info(self) -> dict:
        """Get information about the dataset."""
        return {
            'total_samples': len(self.data),
            'columns': list(self.data.columns),
            'unique_images': self.data['image'].nunique() if 'image' in self.data.columns else 0,
            'label_distribution': self._get_label_distribution()
        }
    
    def _get_label_distribution(self) -> dict:
        """Get distribution of labels in the dataset."""
        distribution = {}
        for col in ['cadran_1', 'cadran_2', 'cadran_3', 'cadran_4']:
            if col in self.data.columns:
                distribution[col] = self.data[col].value_counts().to_dict()
        return distribution


class MultiDigitDataset(BaseDataset):
    """Dataset class for multi-digit (end-to-end) approach."""
    
    def __init__(self, data_dir: str = 'Datasets', frame_dir: str = 'Datasets_frames'):
        """
        Initialize multi-digit dataset.
        
        Args:
            data_dir: Directory containing CSV files
            frame_dir: Directory containing preprocessed frame images
        """
        super().__init__(data_dir)
        self.frame_dir = frame_dir
        self.frame_data = self._filter_available_frames()
        logger.info(f"Found {len(self.frame_data)} frames with labels")
    
    def _filter_available_frames(self) -> pd.DataFrame:
        """Filter data to only include images that exist in frame directory."""
        if not os.path.exists(self.frame_dir):
            logger.warning(f"Frame directory {self.frame_dir} not found")
            return pd.DataFrame()
        
        available_frames = set(os.listdir(self.frame_dir))
        filtered_data = self.data[self.data['image'].isin(available_frames)]
        
        logger.info(f"Filtered from {len(self.data)} to {len(filtered_data)} available frames")
        return filtered_data
    
    def load_images(self, image_names: List[str], target_size: Tuple[int, int] = (100, 400)) -> np.ndarray:
        """
        Load and preprocess images.
        
        Args:
            image_names: List of image filenames
            target_size: Target size (height, width) for images
            
        Returns:
            Numpy array of preprocessed images
        """
        images = []
        
        for img_name in image_names:
            img_path = os.path.join(self.frame_dir, img_name)
            
            try:
                # Load image
                img = Image.open(img_path)
                
                # Convert to grayscale if needed
                if img.mode != 'L':
                    img = img.convert('L')
                
                # Resize to target size
                img = img.resize((target_size[1], target_size[0]))
                
                # Convert to numpy array and normalize
                img_array = np.array(img, dtype=np.float32) / 255.0
                
                # Add channel dimension
                img_array = np.expand_dims(img_array, axis=-1)
                
                images.append(img_array)
                
            except Exception as e:
                logger.error(f"Error loading image {img_name}: {e}")
                # Create a blank image as fallback
                blank_img = np.zeros((*target_size, 1), dtype=np.float32)
                images.append(blank_img)
        
        return np.array(images)
    
    def create_tf_dataset(self, batch_size: int = 32, validation_split: float = 0.2, 
                         shuffle: bool = True, seed: int = 42) -> Tuple[tf.data.Dataset, tf.data.Dataset]:
        """
        Create TensorFlow datasets for training and validation.
        
        Args:
            batch_size: Batch size for training
            validation_split: Fraction of data to use for validation
            shuffle: Whether to shuffle the data
            seed: Random seed for reproducibility
            
        Returns:
            Tuple of (train_dataset, val_dataset)
        """
        if len(self.frame_data) == 0:
            raise ValueError("No frame data available. Check if frame directory exists and contains images.")
        
        # Prepare features and labels
        X = self.frame_data['image'].values
        y = self.frame_data[['cadran_1', 'cadran_2', 'cadran_3', 'cadran_4']].values
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=seed, shuffle=shuffle
        )
        
        # Create datasets
        train_dataset = self._create_dataset_from_arrays(X_train, y_train, batch_size, shuffle=True)
        val_dataset = self._create_dataset_from_arrays(X_val, y_val, batch_size, shuffle=False)
        
        return train_dataset, val_dataset
    
    def _create_dataset_from_arrays(self, X: np.ndarray, y: np.ndarray, 
                                   batch_size: int, shuffle: bool = True) -> tf.data.Dataset:
        """Create TensorFlow dataset from arrays."""
        def generator():
            indices = np.arange(len(X))
            if shuffle:
                np.random.shuffle(indices)
            
            for idx in indices:
                # Load single image
                img = self.load_images([X[idx]])[0]
                labels = y[idx]
                
                # Convert labels to proper format for multi-output model
                label_dict = {
                    'digit_1': labels[0],
                    'digit_2': labels[1], 
                    'digit_3': labels[2],
                    'digit_4': labels[3]
                }
                
                yield img, label_dict
        
        # Define output signature
        output_signature = (
            tf.TensorSpec(shape=(100, 400, 1), dtype=tf.float32),
            {
                'digit_1': tf.TensorSpec(shape=(), dtype=tf.int64),
                'digit_2': tf.TensorSpec(shape=(), dtype=tf.int64),
                'digit_3': tf.TensorSpec(shape=(), dtype=tf.int64),
                'digit_4': tf.TensorSpec(shape=(), dtype=tf.int64)
            }
        )
        
        dataset = tf.data.Dataset.from_generator(generator, output_signature=output_signature)
        dataset = dataset.batch(batch_size)
        dataset = dataset.prefetch(tf.data.AUTOTUNE)
        
        return dataset


class SingleDigitDataset(BaseDataset):
    """Dataset class for single digit classification approach."""
    
    def __init__(self, data_dir: str = 'Datasets', digits_dir: str = 'Datasets_digits'):
        """
        Initialize single digit dataset.
        
        Args:
            data_dir: Directory containing CSV files
            digits_dir: Directory containing individual digit images
        """
        super().__init__(data_dir)
        self.digits_dir = digits_dir
        self.digits_data = self._load_digits_data()
        logger.info(f"Found {len(self.digits_data)} individual digit samples")
    
    def _load_digits_data(self) -> pd.DataFrame:
        """Load individual digit images and their labels."""
        if not os.path.exists(self.digits_dir):
            logger.warning(f"Digits directory {self.digits_dir} not found")
            return pd.DataFrame()
        
        image_paths = []
        labels = []
        
        # Iterate through digit folders (0-10, where 10 represents 'X')
        for digit in range(11):
            digit_folder = os.path.join(self.digits_dir, str(digit))
            
            if os.path.exists(digit_folder):
                for img_file in os.listdir(digit_folder):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(digit_folder, img_file)
                        image_paths.append(img_path)
                        labels.append(digit)
        
        return pd.DataFrame({'image_path': image_paths, 'label': labels})
    
    def load_digit_images(self, image_paths: List[str], target_size: Tuple[int, int] = (64, 32)) -> np.ndarray:
        """
        Load and preprocess individual digit images.
        
        Args:
            image_paths: List of image file paths
            target_size: Target size (height, width) for images
            
        Returns:
            Numpy array of preprocessed images
        """
        images = []
        
        for img_path in image_paths:
            try:
                # Load image
                img = Image.open(img_path)
                
                # Convert to grayscale if needed
                if img.mode != 'L':
                    img = img.convert('L')
                
                # Resize to target size
                img = img.resize((target_size[1], target_size[0]))
                
                # Convert to numpy array and normalize
                img_array = np.array(img, dtype=np.float32) / 255.0
                
                # Add channel dimension
                img_array = np.expand_dims(img_array, axis=-1)
                
                images.append(img_array)
                
            except Exception as e:
                logger.error(f"Error loading image {img_path}: {e}")
                # Create a blank image as fallback
                blank_img = np.zeros((*target_size, 1), dtype=np.float32)
                images.append(blank_img)
        
        return np.array(images)
    
    def create_tf_dataset(self, batch_size: int = 64, validation_split: float = 0.2,
                         shuffle: bool = True, seed: int = 42) -> Tuple[tf.data.Dataset, tf.data.Dataset]:
        """
        Create TensorFlow datasets for training and validation.
        
        Args:
            batch_size: Batch size for training
            validation_split: Fraction of data to use for validation
            shuffle: Whether to shuffle the data
            seed: Random seed for reproducibility
            
        Returns:
            Tuple of (train_dataset, val_dataset)
        """
        if len(self.digits_data) == 0:
            raise ValueError("No digit data available. Check if digits directory exists and contains images.")
        
        # Prepare features and labels
        X = self.digits_data['image_path'].values
        y = self.digits_data['label'].values
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=seed, shuffle=shuffle, stratify=y
        )
        
        # Create datasets
        train_dataset = self._create_digit_dataset(X_train, y_train, batch_size, shuffle=True)
        val_dataset = self._create_digit_dataset(X_val, y_val, batch_size, shuffle=False)
        
        return train_dataset, val_dataset
    
    def _create_digit_dataset(self, X: np.ndarray, y: np.ndarray, 
                             batch_size: int, shuffle: bool = True) -> tf.data.Dataset:
        """Create TensorFlow dataset for digit classification."""
        def generator():
            indices = np.arange(len(X))
            if shuffle:
                np.random.shuffle(indices)
            
            for idx in indices:
                # Load single image
                img = self.load_digit_images([X[idx]])[0]
                label = y[idx]
                
                yield img, label
        
        # Define output signature
        output_signature = (
            tf.TensorSpec(shape=(64, 32, 1), dtype=tf.float32),
            tf.TensorSpec(shape=(), dtype=tf.int64)
        )
        
        dataset = tf.data.Dataset.from_generator(generator, output_signature=output_signature)
        dataset = dataset.batch(batch_size)
        dataset = dataset.prefetch(tf.data.AUTOTUNE)
        
        return dataset


# Convenience functions for easy dataset creation
def create_multi_digit_dataset(data_dir: str = 'Datasets', frame_dir: str = 'Datasets_frames') -> MultiDigitDataset:
    """Create a multi-digit dataset instance."""
    return MultiDigitDataset(data_dir, frame_dir)


def create_single_digit_dataset(data_dir: str = 'Datasets', digits_dir: str = 'Datasets_digits') -> SingleDigitDataset:
    """Create a single digit dataset instance."""
    return SingleDigitDataset(data_dir, digits_dir)
