# Seven-Segment OCR - Modern Implementation

A modernized implementation of Seven-Segment OCR for gas pump displays, optimized for Google Colab with GPU acceleration and TensorFlow 2.x.

## 🚀 Features

- **Modern TensorFlow 2.x** implementation with Keras functional API
- **Google Colab optimized** with GPU acceleration and mixed precision training
- **Two recognition approaches**: Multi-digit (end-to-end) and Single-digit classification
- **Advanced data augmentation** for improved generalization
- **Automated setup scripts** for easy deployment
- **Comprehensive evaluation** with detailed metrics and visualizations

## 📊 Dataset

The project works with gas pump display images containing seven-segment digits:
- **~850 labeled images** across three quality levels (HQ, MQ, LQ)
- **CSV labels** with digit positions and values
- **Preprocessed frames** ready for training
- **Individual digit images** for single-digit classification

## 🛠️ Installation

### Google Colab (Recommended)

1. **Open the notebook**: Upload `Seven_Segment_OCR_Modern.ipynb` to Google Colab
2. **Run setup cell**: The notebook includes automated setup
3. **Upload your data**: Use the file upload interface in Colab
4. **Start training**: Follow the notebook instructions

### Local Installation

```bash
# Clone the repository
git clone <your-repository-url>
cd Seven-Segment-OCR-Modern

# Install dependencies
pip install -r requirements.txt

# Run automated setup
python setup.py
```

## 📁 Project Structure

```
Seven-Segment-OCR-Modern/
├── Seven_Segment_OCR_Modern.ipynb    # Main training notebook
├── datasets.py                       # Modern dataset classes
├── models.py                         # CNN model architectures
├── frame_extractor.py               # Frame preprocessing
├── digit_cutter.py                  # Digit segmentation
├── data_augmentation.py             # Advanced augmentation
├── setup.py                         # Automated setup
├── requirements.txt                 # Dependencies
├── README.md                        # This file
├── Datasets/                        # CSV label files
├── Datasets_frames/                 # Preprocessed frame images
├── Datasets_digits/                 # Individual digit images
├── models/                          # Saved models
└── logs/                           # Training logs
```

## 🎯 Quick Start

### 1. Google Colab (Easiest)

```python
# In a Colab cell
!git clone <your-repository-url>
%cd Seven-Segment-OCR-Modern

# Run the setup
from setup import SevenSegmentOCRSetup
setup = SevenSegmentOCRSetup(environment='colab')
setup.run_complete_setup()

# Open and run the notebook
```

### 2. Local Development

```python
# Run setup
from setup import SevenSegmentOCRSetup
setup = SevenSegmentOCRSetup()
setup.run_complete_setup()

# Load and train model
from datasets import MultiDigitDataset
from models import MultiDigitModel

# Create dataset
dataset = MultiDigitDataset()
X_train, y_train = dataset.load_training_data()

# Create and train model
model = MultiDigitModel()
model.build_model()
model.train(X_train, y_train)
```

## 🏗️ Model Architecture

### Multi-Digit Model (End-to-End)
- **Input**: Full frame image (100x400x1)
- **Backbone**: CNN with BatchNormalization and Dropout
- **Output**: 4 separate digit predictions (0-9 + unknown)
- **Loss**: Sparse categorical crossentropy for each digit

### Single-Digit Model
- **Input**: Individual digit image (64x32x1)
- **Architecture**: Lightweight CNN for single digit classification
- **Output**: Single digit prediction (0-9 + unknown)

## 📈 Performance Optimization

### Data Augmentation
- **Rotation**: ±5 degrees
- **Brightness/Contrast**: ±20% variation
- **Noise**: Gaussian noise injection
- **Blur**: Random Gaussian blur
- **Elastic deformation**: Realistic variations

### Training Optimizations
- **Mixed precision training** for faster GPU utilization
- **Learning rate scheduling** with exponential decay
- **Early stopping** and model checkpointing
- **TensorBoard logging** for monitoring
- **Optimized data pipeline** with prefetching

## 📊 Results

Expected performance on the gas pump dataset:
- **Individual digit accuracy**: 95-98%
- **Complete sequence accuracy**: 85-92%
- **Training time**: 10-20 minutes on Colab GPU

## 🔧 Advanced Usage

### Custom Data Augmentation

```python
from data_augmentation import AdvancedDataAugmentation

# Create custom augmentation
augmentation = AdvancedDataAugmentation(
    rotation_range=10.0,
    brightness_range=(0.7, 1.3),
    noise_factor=0.1
)

# Apply to dataset
augmented_dataset = augmentation.create_augmented_dataset(images, labels)
```

### Performance Monitoring

```python
from data_augmentation import setup_training_environment

# Setup optimized training environment
env = setup_training_environment("my_model")
strategy = env['strategy']
callbacks = env['callbacks']

# Train with optimizations
with strategy.scope():
    model = create_model()
    model.fit(dataset, callbacks=callbacks)
```

## 🐛 Troubleshooting

### Common Issues

1. **GPU not detected**:
   ```python
   import tensorflow as tf
   print(tf.config.list_physical_devices('GPU'))
   ```

2. **Memory errors**:
   - Reduce batch size in the notebook
   - Enable memory growth in GPU setup

3. **Import errors**:
   - Run `pip install -r requirements.txt`
   - Restart runtime in Colab

4. **Data loading issues**:
   - Check file paths in dataset configuration
   - Verify CSV file format (semicolon-separated)

### Performance Tips

- **Use GPU**: Enable GPU runtime in Colab
- **Batch size**: Start with 32, adjust based on memory
- **Data augmentation**: Use 'medium' strength for best results
- **Early stopping**: Set patience=10 for optimal training time

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the notebook comments
3. Open an issue on GitHub
4. Check TensorFlow/Colab documentation

## 🙏 Acknowledgments

- Original Seven-Segment OCR implementation
- TensorFlow and Keras teams
- Google Colab for free GPU access
- OpenCV community for image processing tools

---

**Happy training! 🚀**

For more detailed instructions, see the `Seven_Segment_OCR_Modern.ipynb` notebook.
