"""
Modern Frame Extraction for Seven-Segment OCR
Updated for current OpenCV and image processing best practices
"""

import os
import shutil
import numpy as np
import cv2
import scipy.spatial as sp
from skimage.measure import label, regionprops
import glob
from typing import Tuple, Optional, Union
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModernFrameExtractor:
    """
    Modern frame extractor for LCD/LED displays with improved error handling
    and processing pipeline.
    """
    
    def __init__(self, 
                 image: Optional[np.ndarray] = None,
                 src_file_name: Optional[str] = None,
                 dst_file_name: Optional[str] = None,
                 return_image: bool = False,
                 output_shape: Tuple[int, int] = (400, 100)):
        """
        Initialize frame extractor.
        
        Args:
            image: RGB image array with screen to extract
            src_file_name: Source image filename
            dst_file_name: Destination filename for processed image
            return_image: Whether to return processed image
            output_shape: Output image dimensions (width, height)
        """
        self.src_file_name = src_file_name
        self.dst_file_name = dst_file_name
        self.return_image = return_image
        self.output_shape = output_shape
        
        # Load image
        if image is not None:
            self.image = image
        elif src_file_name is not None:
            self.image = self._load_image(src_file_name)
        else:
            raise ValueError("Either image array or src_file_name must be provided")
        
        # Processing results
        self.raw_frame = None
        self.processed_frame = None
        self.sliced_frame = None
        
    def _load_image(self, filepath: str) -> np.ndarray:
        """Load image with error handling."""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Image file not found: {filepath}")
        
        image = cv2.imread(filepath)
        if image is None:
            raise ValueError(f"Could not load image: {filepath}")
        
        return image
    
    @staticmethod
    def sort_points_clockwise(points: np.ndarray) -> np.ndarray:
        """
        Sort points in clockwise order starting from top-left.
        
        Args:
            points: Array of 2D points
            
        Returns:
            Points sorted clockwise from top-left
        """
        # Sort by y-coordinate
        sorted_by_y = points[np.argsort(points[:, 1]), :]
        
        # Get top and bottom pairs
        top_points = sorted_by_y[:2, :]
        bottom_points = sorted_by_y[2:, :]
        
        # Sort top points by x-coordinate (left to right)
        top_sorted = top_points[np.argsort(top_points[:, 0]), :]
        top_left = top_sorted[0, :]
        
        # Calculate distances from top-left to bottom points
        distances = sp.distance.cdist(top_left[None], bottom_points, 'euclidean')[0]
        bottom_sorted = bottom_points[np.argsort(distances)[::-1], :]
        
        # Concatenate: top-left, top-right, bottom-right, bottom-left
        return np.concatenate([top_sorted, bottom_sorted], axis=0)
    
    @staticmethod
    def adjust_gamma(image: np.ndarray, gamma: float = 1.0) -> np.ndarray:
        """
        Adjust image gamma for illumination correction.
        
        Args:
            image: Grayscale image
            gamma: Gamma value (< 1 darkens, > 1 brightens)
            
        Returns:
            Gamma-corrected image
        """
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 
                         for i in np.arange(0, 256)]).astype(np.uint8)
        return cv2.LUT(image.astype(np.uint8), table)
    
    def _distance_from_center(self, rectangle: np.ndarray) -> float:
        """
        Calculate distance of rectangle center from image center.
        
        Args:
            rectangle: 4x2 array of rectangle corners
            
        Returns:
            Distance from image center
        """
        rect_center = 0.5 * (rectangle[0] + rectangle[2])
        image_center = 0.5 * np.array([self.image.shape[1], self.image.shape[0]])
        return np.linalg.norm(rect_center - image_center)
    
    def detect_frame(self) -> bool:
        """
        Detect and extract the display frame from the image.
        
        Returns:
            True if frame detection successful, False otherwise
        """
        try:
            # Resize image for consistent processing
            height = 500
            self.image = self._resize_image(self.image, height)
            
            # Step 1: Preprocessing
            gray = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            gamma_corrected = self.adjust_gamma(blurred, gamma=0.7)
            
            # Binary threshold using Otsu's method
            _, binary_mask = cv2.threshold(
                gamma_corrected, 0, 255, 
                cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU
            )
            
            # Step 2: Find regions of interest
            labeled_image = label(binary_mask)
            best_contour = None
            best_position = None
            min_distance = float('inf')
            
            for region in regionprops(labeled_image):
                minr, minc, maxr, maxc = region.bbox
                
                # Create rectangle from bounding box
                corners = np.array([
                    [minc, minr], [minc, maxr], 
                    [maxc, minr], [maxc, maxr]
                ])
                
                # Apply heuristics
                width = maxc - minc
                height = maxr - minr
                area = cv2.contourArea(corners)
                min_area = 0.05 * (binary_mask.shape[0] * binary_mask.shape[1])
                
                # Check if region meets criteria
                if (width > height and  # Landscape orientation
                    area > min_area and  # Minimum size
                    self._distance_from_center(corners) < min_distance):
                    
                    min_distance = self._distance_from_center(corners)
                    best_contour = self.sort_points_clockwise(corners)
                    best_position = [minr, minc, maxr, maxc]
            
            if best_contour is None:
                logger.warning("No suitable frame region found")
                return False
            
            # Step 3: Refined detection in cropped region
            success = self._refine_frame_detection(best_position)
            
            if not success:
                # Fallback to basic perspective transform
                self._apply_basic_transform(best_contour, gray)
            
            return True
            
        except Exception as e:
            logger.error(f"Frame detection failed: {e}")
            return False
    
    def _resize_image(self, image: np.ndarray, target_height: int) -> np.ndarray:
        """Resize image maintaining aspect ratio."""
        h, w = image.shape[:2]
        if h != target_height:
            scale = target_height / h
            new_width = int(w * scale)
            image = cv2.resize(image, (new_width, target_height))
        return image
    
    def _refine_frame_detection(self, position: list) -> bool:
        """
        Refine frame detection in cropped region.
        
        Args:
            position: [minr, minc, maxr, maxc] of initial detection
            
        Returns:
            True if refinement successful
        """
        try:
            # Crop with padding
            padding = 30
            minr, minc, maxr, maxc = position
            
            crop_img = self.image[
                max(0, minr - padding):min(maxr + padding, self.image.shape[0]),
                max(0, minc - padding):min(maxc + padding, self.image.shape[1])
            ]
            
            # Enhanced preprocessing for cropped region
            crop_blurred = cv2.GaussianBlur(crop_img, (5, 5), 0)
            crop_gamma = self.adjust_gamma(crop_blurred, gamma=0.4)
            crop_gray = cv2.cvtColor(crop_gamma, cv2.COLOR_BGR2GRAY)
            
            _, crop_thresh = cv2.threshold(
                crop_gray, 0, 255, 
                cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU
            )
            
            # Find contours
            contours, _ = cv2.findContours(
                crop_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )
            
            if not contours:
                return False
            
            # Sort by area and find rectangular contour
            contours = sorted(contours, key=cv2.contourArea, reverse=True)
            
            for contour in contours:
                perimeter = cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)
                
                if len(approx) == 4:
                    area = cv2.contourArea(approx)
                    min_area = 0.5 * (crop_img.shape[0] * crop_img.shape[1])
                    
                    if area >= min_area:
                        # Apply perspective transform
                        self._apply_perspective_transform(approx, crop_img)
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Frame refinement failed: {e}")
            return False
    
    def _apply_perspective_transform(self, contour: np.ndarray, source_image: np.ndarray) -> None:
        """Apply perspective transformation to extract frame."""
        # Sort contour points
        contour = contour.reshape(4, 2)
        sorted_points = self.sort_points_clockwise(contour)
        
        # Define destination points
        dst_points = np.array([
            [0, 0], [self.output_shape[0], 0],
            [self.output_shape[0], self.output_shape[1]], [0, self.output_shape[1]]
        ], dtype=np.float32)
        
        # Calculate perspective transform matrix
        transform_matrix = cv2.getPerspectiveTransform(
            sorted_points.astype(np.float32), dst_points
        )
        
        # Apply transformation
        self.raw_frame = cv2.warpPerspective(
            source_image, transform_matrix, self.output_shape
        )
    
    def _apply_basic_transform(self, contour: np.ndarray, gray_image: np.ndarray) -> None:
        """Apply basic perspective transform as fallback."""
        dst_points = np.array([
            [0, 0], [self.output_shape[0], 0],
            [self.output_shape[0], self.output_shape[1]], [0, self.output_shape[1]]
        ], dtype=np.float32)
        
        transform_matrix = cv2.getPerspectiveTransform(
            contour.astype(np.float32), dst_points
        )
        
        self.raw_frame = cv2.warpPerspective(
            gray_image, transform_matrix, self.output_shape
        )
    
    def preprocess_frame(self) -> None:
        """Apply final preprocessing to enhance digit visibility."""
        if self.raw_frame is None:
            raise ValueError("Frame must be detected before preprocessing")
        
        try:
            # Convert to grayscale if needed
            if len(self.raw_frame.shape) == 3:
                gray = cv2.cvtColor(self.raw_frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = self.raw_frame.copy()
            
            # Histogram equalization for better contrast
            equalized = cv2.equalizeHist(gray)
            
            # Binary threshold
            _, thresh = cv2.threshold(equalized, 45, 255, cv2.THRESH_BINARY_INV)
            
            # Morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            self.processed_frame = cleaned
            
        except Exception as e:
            logger.error(f"Frame preprocessing failed: {e}")
            # Fallback to simple processing
            if len(self.raw_frame.shape) == 3:
                self.processed_frame = cv2.cvtColor(self.raw_frame, cv2.COLOR_BGR2GRAY)
            else:
                self.processed_frame = self.raw_frame.copy()
    
    def slice_frame(self, slice_ratio: float = 8/13) -> None:
        """
        Slice frame to keep only integer part (remove decimal).
        
        Args:
            slice_ratio: Ratio at which to slice (default removes decimal part)
        """
        if self.processed_frame is None:
            raise ValueError("Frame must be processed before slicing")
        
        stop_at = int(np.floor(self.output_shape[0] * slice_ratio))
        self.sliced_frame = self.processed_frame[:, :stop_at].copy()
    
    def extract_and_save_frame(self) -> Optional[np.ndarray]:
        """
        Complete frame extraction pipeline.
        
        Returns:
            Extracted frame if return_image is True, None otherwise
        """
        try:
            # Execute pipeline
            if not self.detect_frame():
                logger.error("Frame detection failed")
                return None
            
            self.preprocess_frame()
            self.slice_frame()
            
            # Save if destination specified
            if self.dst_file_name:
                # Ensure directory exists
                os.makedirs(os.path.dirname(self.dst_file_name), exist_ok=True)
                cv2.imwrite(self.dst_file_name, self.sliced_frame)
                logger.debug(f"Frame saved to {self.dst_file_name}")
            
            # Return image if requested
            if self.return_image:
                return self.sliced_frame
            
            return None
            
        except Exception as e:
            logger.error(f"Frame extraction failed: {e}")
            return None


def batch_extract_frames(input_pattern: str, output_dir: str, 
                        output_shape: Tuple[int, int] = (400, 100)) -> dict:
    """
    Extract frames from multiple images in batch.
    
    Args:
        input_pattern: Glob pattern for input images
        output_dir: Output directory for processed frames
        output_shape: Output image dimensions
        
    Returns:
        Dictionary with processing statistics
    """
    # Setup output directory
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    # Process images
    input_files = glob.glob(input_pattern)
    stats = {'total': len(input_files), 'success': 0, 'failed': 0}
    
    logger.info(f"Processing {stats['total']} images...")
    
    for input_file in input_files:
        try:
            filename = os.path.basename(input_file)
            output_file = os.path.join(output_dir, filename)
            
            extractor = ModernFrameExtractor(
                src_file_name=input_file,
                dst_file_name=output_file,
                output_shape=output_shape
            )
            
            result = extractor.extract_and_save_frame()
            
            if result is not None or os.path.exists(output_file):
                stats['success'] += 1
            else:
                stats['failed'] += 1
                
        except Exception as e:
            logger.error(f"Failed to process {input_file}: {e}")
            stats['failed'] += 1
    
    logger.info(f"Batch processing complete: {stats['success']} success, {stats['failed']} failed")
    return stats


# Main execution for batch processing
if __name__ == "__main__":
    # Process all quality levels
    datasets = [
        ('Datasets/HQ_digital/*jpg', 'HQ'),
        ('Datasets/MQ_digital/*jpg', 'MQ'), 
        ('Datasets/LQ_digital/*jpg', 'LQ')
    ]
    
    total_stats = {'total': 0, 'success': 0, 'failed': 0}
    
    for pattern, quality in datasets:
        logger.info(f"Processing {quality} quality images...")
        stats = batch_extract_frames(pattern, 'Datasets_frames/')
        
        total_stats['total'] += stats['total']
        total_stats['success'] += stats['success']
        total_stats['failed'] += stats['failed']
        
        logger.info(f"{quality} results: {stats}")
    
    logger.info(f"Overall results: {total_stats}")
    success_rate = (total_stats['success'] / total_stats['total']) * 100 if total_stats['total'] > 0 else 0
    logger.info(f"Success rate: {success_rate:.1f}%")
